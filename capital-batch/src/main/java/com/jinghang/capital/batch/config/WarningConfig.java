package com.jinghang.capital.batch.config;

import brave.Tracer;
import com.jinghang.capital.batch.service.WarningService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.lang.Nullable;

/**
 * <AUTHOR>
 * @date 2023/7/4
 */
@Configuration
public class WarningConfig {

    @Value("${warning.key.default}")
    private String defaultWarningKey;

    @Value("${warning.state.key.default}")
    private String stateWarningKey;

    @Value("${warning.repay.key}")
    private String repayWarningKey;
    @Bean
    public WarningService warningService(@Nullable Tracer tracer) {
        return new WarningService(defaultWarningKey, tracer);
    }

    @Bean
    public WarningService warningStateService(@Nullable Tracer tracer) {
        return new WarningService(stateWarningKey, tracer);
    }

    @Bean
    public WarningService warningRepayService(@Nullable Tracer tracer) {
        return new WarningService(repayWarning<PERSON><PERSON>, tracer);
    }
}

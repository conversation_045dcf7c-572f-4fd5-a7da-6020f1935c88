package com.jinghang.capital.core.banks.hxbk.enums;

/**
 * 大类编码
 * 01-风控报告
 * 10-合同
 * 20身份证图片
 * 26人脸图片
 * 30-附件
 */
public enum HXBKMaterialsBigCode {

    RISK_REPORT("01", "风控报告"),
    CONTRACT("10", "合同"),
    ID_PICTURE("20", "身份证图片"),
    FACE("26", "人脸图片"),
    BACKUP("30", "附件");
    private String code;
    private String desc;

    HXBKMaterialsBigCode(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}

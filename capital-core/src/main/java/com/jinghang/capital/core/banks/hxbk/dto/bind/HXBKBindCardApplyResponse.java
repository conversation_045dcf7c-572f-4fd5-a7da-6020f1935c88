package com.jinghang.capital.core.banks.hxbk.dto.bind;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class HXBKBindCardApplyResponse {


    /**
     * 结果code
     */
    @JsonProperty("result_code")
    private String resultCode;

    /**
     * 结果信息
     */
    @JsonProperty("result_msg")
    private String resultMsg;

    /**
     * 请求唯一id
     */
    @JsonProperty("req_msg_id")
    private String reqMsgId;

    /**
     * 绑卡流水号
     */
    @JsonProperty("bind_serial_no")
    private String bindSerialNo;

    /**
     * 客户编号
     */
    @JsonProperty("customer_no")
    private String customerNo;

    public String getResultCode() {
        return resultCode;
    }

    public void setResultCode(String resultCode) {
        this.resultCode = resultCode;
    }

    public String getResultMsg() {
        return resultMsg;
    }

    public void setResultMsg(String resultMsg) {
        this.resultMsg = resultMsg;
    }

    public String getReqMsgId() {
        return reqMsgId;
    }

    public void setReqMsgId(String reqMsgId) {
        this.reqMsgId = reqMsgId;
    }

    public String getBindSerialNo() {
        return bindSerialNo;
    }

    public void setBindSerialNo(String bindSerialNo) {
        this.bindSerialNo = bindSerialNo;
    }

    public String getCustomerNo() {
        return customerNo;
    }

    public void setCustomerNo(String customerNo) {
        this.customerNo = customerNo;
    }
}

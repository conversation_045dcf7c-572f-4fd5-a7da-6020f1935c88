package com.jinghang.capital.core.banks.hxbk.recc;


import com.jinghang.capital.core.banks.hxbk.enums.HXBKReccFileType;
import com.jinghang.capital.core.entity.BankRepayRecord;
import com.jinghang.capital.core.entity.CYBKReconcileFile;
import com.jinghang.capital.core.entity.Loan;
import com.jinghang.capital.core.entity.hxbk.HXBKReccLoan;
import com.jinghang.capital.core.entity.hxbk.HXBKReccRepay;
import com.jinghang.capital.core.enums.BankChannel;
import com.jinghang.capital.core.enums.LoanStatus;
import com.jinghang.capital.core.enums.ProcessStatus;
import com.jinghang.capital.core.enums.ReccStateEnum;
import com.jinghang.capital.core.enums.RepayType;
import com.jinghang.capital.core.exception.BizErrorCode;
import com.jinghang.capital.core.exception.BizException;
import com.jinghang.capital.core.repository.BankRepayRecordRepository;
import com.jinghang.capital.core.repository.CYBKReconcileFileRepository;
import com.jinghang.capital.core.repository.HXBKReccLoanRepository;
import com.jinghang.capital.core.repository.HXBKReccRepayRepository;
import com.jinghang.capital.core.repository.LoanRepository;
import com.jinghang.capital.core.service.WarningService;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2025/07/10
 */
public abstract class HXBKReccAbstractHandler implements HXBKReccHandler {

    private static final Logger logger = LoggerFactory.getLogger(HXBKReccAbstractHandler.class);
    @Autowired
    private CYBKReconcileFileRepository cybkReconcileFileRepository;

    @Autowired
    private WarningService warningService;

    @Autowired
    private LoanRepository loanRepository;

    @Autowired
    private HXBKReccLoanRepository hxbkReccLoanRepository;
    @Autowired
    private HXBKReccRepayRepository hxbkReccRepayRepository;
    @Autowired
    private BankRepayRecordRepository bankRepayRecordRepository;

    public WarningService getWarningService() {
        return warningService;
    }

    public CYBKReconcileFile findReconcileFile(LocalDate reccDay, HXBKReccFileType reccFileType) {
        List<CYBKReconcileFile> reconFiles = cybkReconcileFileRepository.findByChannelAndFileDateAndReccType(BankChannel.HXBK.name(),
                reccDay, reccFileType.name());
        long hasSuccessCount = reconFiles.stream().filter(rf -> ReccStateEnum.S.name().equals(rf.getReccState())).count();
        if (hasSuccessCount > 0) {
            logger.error("HXBK recc process reccDay: {}, type: {} has success processed.", reccDay, reccFileType);
            warningService.warn("\n湖消对账:" + reccFileType.name() + "\n对账日:" + reccDay + "\n已经对账成功");
            throw new BizException(BizErrorCode.REC_HAS_SUCCESS);
        }

        List<CYBKReconcileFile> list = reconFiles.stream().filter(rf -> ReccStateEnum.P.name().equals(rf.getReccState())).toList();

        if (list.size() == 0) {
            logger.error("HXBK recc process reccDay: {}, type: {} has no process file.", reccDay, reccFileType);
            warningService.warn("\n湖消对账:" + reccFileType.name() + "\n对账日:" + reccDay + "\n没有待处理记录");
            throw new BizException(BizErrorCode.REC_NOT_FOUND);
        }

        if (list.size() > 1) {
            logger.error("HXBK recc process reccDay: {}, type: {} has multi process file.", reccDay, reccFileType);
            warningService.warn("\n湖消直连对账:" + reccFileType.name() + "\n对账日:" + reccDay + "\n存在多条待处理记录");
            throw new BizException(BizErrorCode.REC_MULTI_PROCESSING_FILE);
        }
        return list.get(0);

    }

    public List<Loan> findReccLoans(LocalDate reccDay) {
        LocalDateTime dayStart = reccDay.atStartOfDay();
        LocalDateTime nextDayStart = reccDay.plusDays(1L).atStartOfDay();
        return findLoans0(dayStart, nextDayStart);
    }

    /**
     * 查询指定时间段内成功的借据
     */
    public List<Loan> findLoans0(LocalDateTime dayStart, LocalDateTime nextDayStart) {
        return loanRepository.findByChannelAndLoanStatusAndLoanTime(
                BankChannel.HXBK, LoanStatus.SUCCESS, dayStart, nextDayStart);
    }

    public List<HXBKReccLoan> findReccLoanFileRecords(String reccId) {
        return hxbkReccLoanRepository.findByReccId(reccId);
    }

    public CYBKReconcileFile updateCYBKReconcileFile(CYBKReconcileFile reconcileFile) {
        return cybkReconcileFileRepository.save(reconcileFile);
    }

    public HXBKReccLoan updateReccLoan(HXBKReccLoan reccLoan) {
        return hxbkReccLoanRepository.save(reccLoan);
    }

    public List<HXBKReccRepay> findReccRepayDetailFileRecords(String reccId) {
        return hxbkReccRepayRepository.findByReccId(reccId);
    }

    public List<BankRepayRecord> findSuccessBankRepayRecordNormal(LocalDate reccDay, boolean isClaim) {
        LocalDateTime dayStart = reccDay.atStartOfDay();
        LocalDateTime nextDayStart = reccDay.plusDays(1L).atStartOfDay();
        return isClaim ? findSuccessBankRepayRecordClaim(dayStart, nextDayStart) : findSuccessBankRepayRecordNotClaim(dayStart, nextDayStart);
    }

    /**
     * 对资正常还款
     */
    public List<BankRepayRecord> findSuccessBankRepayRecordNotClaim(LocalDateTime dayStart, LocalDateTime nextDayStart) {
        List<BankRepayRecord> records = findSuccessBankRepayRecord(dayStart, nextDayStart);
        return records.stream().filter(r -> r.getRepayType() != RepayType.CLAIM).toList();
    }

    public List<BankRepayRecord> findSuccessBankRepayRecordClaim(LocalDateTime dayStart, LocalDateTime nextDayStart) {
        List<BankRepayRecord> records = findSuccessBankRepayRecord(dayStart, nextDayStart);
        return records.stream().filter(r -> r.getRepayType() == RepayType.CLAIM).toList();
    }

    public List<BankRepayRecord> findSuccessBankRepayRecord(LocalDateTime dayStart, LocalDateTime nextDayStart) {
        return bankRepayRecordRepository.findByRepayStatusAndChannelAndRepayTimeIsGreaterThanEqualAndRepayTimeIsLessThan(
                ProcessStatus.SUCCESS, BankChannel.HXBK, dayStart, nextDayStart);
    }

    public HXBKReccRepay updateReccRepay(HXBKReccRepay reccLoan) {
        return hxbkReccRepayRepository.save(reccLoan);
    }

}

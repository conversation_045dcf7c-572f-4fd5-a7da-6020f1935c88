package com.jinghang.capital.core.banks.hxbk.dto.repay;


import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.math.BigDecimal;


@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class HXBKRepayTrialResponse {

    /**
     * 还款本金
     */
    @JsonProperty("real_principal")
    private BigDecimal realPrincipal;
    /**
     * 还款利息
     */
    @JsonProperty("real_interest")
    private BigDecimal realInterest;
    /**
     * 还款费用
     */
    @JsonProperty("real_over_amt")
    private BigDecimal realOverAmt;
    /**
     * 服务费
     */
    @JsonProperty("service_charge")
    private BigDecimal serviceCharge;
    /**
     * 担保费
     */
    @JsonProperty("real_guarantee_fee")
    private BigDecimal realGuaranteeFee;
    /**
     * 违约金
     */
    @JsonProperty("real_liquidated_damages")
    private BigDecimal realLiquidatedDamages;

    /**
     * 结果code
     */
    @JsonProperty("result_code")
    private String resultCode;
    /**
     * 结果信息
     */
    @JsonProperty("result_msg")
    private String resultMsg;
    /**
     * 请求唯一id
     */
    @JsonProperty("req_msg_id")
    private String reqMsgId;

    public BigDecimal getRealGuaranteeFee() {
        return realGuaranteeFee;
    }

    public void setRealGuaranteeFee(BigDecimal realGuaranteeFee) {
        this.realGuaranteeFee = realGuaranteeFee;
    }

    public BigDecimal getRealInterest() {
        return realInterest;
    }

    public void setRealInterest(BigDecimal realInterest) {
        this.realInterest = realInterest;
    }

    public BigDecimal getRealLiquidatedDamages() {
        return realLiquidatedDamages;
    }

    public void setRealLiquidatedDamages(BigDecimal realLiquidatedDamages) {
        this.realLiquidatedDamages = realLiquidatedDamages;
    }

    public BigDecimal getRealOverAmt() {
        return realOverAmt;
    }

    public void setRealOverAmt(BigDecimal realOverAmt) {
        this.realOverAmt = realOverAmt;
    }

    public BigDecimal getRealPrincipal() {
        return realPrincipal;
    }

    public void setRealPrincipal(BigDecimal realPrincipal) {
        this.realPrincipal = realPrincipal;
    }

    public BigDecimal getServiceCharge() {
        return serviceCharge;
    }

    public void setServiceCharge(BigDecimal serviceCharge) {
        this.serviceCharge = serviceCharge;
    }

    public String getReqMsgId() {
        return reqMsgId;
    }

    public void setReqMsgId(String reqMsgId) {
        this.reqMsgId = reqMsgId;
    }

    public String getResultCode() {
        return resultCode;
    }

    public void setResultCode(String resultCode) {
        this.resultCode = resultCode;
    }

    public String getResultMsg() {
        return resultMsg;
    }

    public void setResultMsg(String resultMsg) {
        this.resultMsg = resultMsg;
    }

}

package com.jinghang.capital.core.banks.cybk.service;

import com.alibaba.fastjson.JSONObject;
import com.jinghang.capital.core.banks.AbstractBankCallbackService;
import com.jinghang.capital.core.banks.cybk.dto.CYBKCallBackCommonResult;
import com.jinghang.capital.core.banks.cybk.dto.CYBKCallBackQuotaResult;
import com.jinghang.capital.core.enums.BankChannel;
import com.jinghang.capital.core.enums.ProcessStatus;
import com.jinghang.capital.core.service.CommonService;
import com.jinghang.capital.core.service.LockService;
import com.jinghang.capital.core.service.Locker;
import com.jinghang.capital.core.service.WarningService;
import java.time.Duration;
import java.util.Collection;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> gale
 * @Classname CYBKCallbackService
 * @Description
 * @Date 2025/5/14 00:53
 */
@Service
public class CYBKCallbackService extends AbstractBankCallbackService {

    private static final Logger logger = LoggerFactory.getLogger(CYBKCallbackService.class);
    private final int five = 5;
    private final int waitTime = 2;
    @Autowired
    private WarningService warningService;
    @Autowired
    private CommonService commonService;
    @Autowired
    private CYBKCreditService cybkCreditService;
    @Autowired
    private CYBKLoanService cybkLoanService;
    @Autowired
    private CYBKRepayService cybkRepayService;
    @Autowired
    private CYBKQuotaService cybkQuotaService;
    @Autowired
    private LockService lockService;

    public static boolean hasKeyWithValue(JSONObject json, String key) {
        if (!json.containsKey(key)) {
            return false; // Key 不存在
        }

        Object value = json.get(key);

        // 根据具体类型判断是否“有值”
        if (value == null) {
            return false; // 排除 null
        }

        if (value instanceof String) {
            return !((String) value).isEmpty(); // 字符串非空
        }

        if (value instanceof Collection) {
            return !((Collection<?>) value).isEmpty(); // 集合非空
        }

        if (value instanceof Map) {
            return !((Map<?, ?>) value).isEmpty(); // Map 非空
        }

        if (value instanceof Object[]) {
            return ((Object[]) value).length > 0; // 数组非空
        }

        // 其他类型（如 Number、Boolean 等）直接视为有值
        return true;
    }

    /**
     * 授信结果回调
     *
     * @param json
     */
    @Override
    public String creditResultCallback(String json) {
        logger.info("长银授信结果回调入参：{}", json);
        try {
            JSONObject jsonObject = JSONObject.parseObject(json);
            //外部授信流水号即为授信表主键id
            if (!hasKeyWithValue(jsonObject, "outApplSeq")) {
                return CYBKCallBackCommonResult.fail("外部授信流水号outApplSeq不能为空");
            }
            String creditId = jsonObject.getString("outApplSeq");

            //获取回调状态
            if (!hasKeyWithValue(jsonObject, "outSts")) {
                return CYBKCallBackCommonResult.fail("外部状态outSts不能为空");
            }
            ProcessStatus creditCallBackResultStatus;
            String outSts = jsonObject.getString("outSts");
            switch (outSts) {
                case "01":
                    return CYBKCallBackCommonResult.success();
                case "02":
                    creditCallBackResultStatus = ProcessStatus.FAIL;
                    break;
                case "03":
                    creditCallBackResultStatus = ProcessStatus.SUCCESS;
                    break;
                default:
                    getWarningService().warn("未知回调授信状态，授信表id：" + creditId + "，outSts：" + outSts);
                    return CYBKCallBackCommonResult.fail("未知回调授信状态：" + outSts);
            }
            String redisKey = "fin_creditId_query_" + creditId;
            Locker lock = lockService.getLock(redisKey);
            boolean locked;
            try {
                locked = lock.tryLock(Duration.ofSeconds(waitTime), Duration.ofSeconds(five));
                if (!locked) {
                    logger.warn("长银授信结果回调获取锁失败，creditId：{}", creditId);
                    return CYBKCallBackCommonResult.fail();
                }
                return cybkCreditService.doCreditResultCallback(creditId, creditCallBackResultStatus);
            } finally {
                try {
                    lock.unlock();
                } catch (Exception e) {
                    logger.error("长银授信结果回调释放锁异常，creditId：{}", creditId, e);
                }
            }
        } catch (Exception e) {
            logger.error("长银授信结果回调失败：{}", e.getMessage(), e);
            return CYBKCallBackCommonResult.fail();
        }
    }

    /**
     * 放款结果回调
     *
     * @param json
     */
    @Override
    public String loanResultCallback(String json) {
        logger.info("长银放款结果回调入参：{}", json);
        try {
            JSONObject jsonObject = JSONObject.parseObject(json);
            //外部放款流水号即为借款表的主键id
            if (!hasKeyWithValue(jsonObject, "outLoanSeq")) {
                return CYBKCallBackCommonResult.fail("外部放款流水号outLoanSeq不能为空");
            }
            String loanId = jsonObject.getString("outLoanSeq");
            //获取回调状态
            if (!hasKeyWithValue(jsonObject, "dnSts")) {
                return CYBKCallBackCommonResult.fail("放款状态dnSts不能为空");
            }
            ProcessStatus loanCallBackResultStatus;
            String dnSts = jsonObject.getString("dnSts");
            switch (dnSts) {
                case "100":
                    return CYBKCallBackCommonResult.success();
                case "200":
                    loanCallBackResultStatus = ProcessStatus.SUCCESS;
                    break;
                case "300":
                    loanCallBackResultStatus = ProcessStatus.FAIL;
                    break;
                default:
                    getWarningService().warn("未知放款结果回调状态，借款表id：" + loanId + "，dnSts：" + dnSts);
                    return CYBKCallBackCommonResult.fail("未知放款结果回调状态：" + dnSts);
            }
            String redisKey = "fin_loanId_query_" + loanId;
            Locker lock = lockService.getLock(redisKey);
            boolean locked;
            try {
                locked = lock.tryLock(Duration.ofSeconds(waitTime), Duration.ofSeconds(five));
                if (!locked) {
                    logger.warn("长银放款结果回调获取锁失败，loanId：{}", loanId);
                    return CYBKCallBackCommonResult.fail();
                }
                return cybkLoanService.doLoanResultCallback(loanId, loanCallBackResultStatus);
            } finally {
                try {
                    lock.unlock();
                } catch (Exception e) {
                    logger.error("长银放款结果回调释放锁异常，loanId：{}", loanId, e);
                }
            }
        } catch (Exception e) {
            logger.error("长银放款结果回调失败：{}", e.getMessage(), e);
            return CYBKCallBackCommonResult.fail();
        }
    }

    /**
     * 调额结果回调
     *
     * @param json
     */
    @Override
    public String quotaAdjustResultCallback(String json) {
        logger.info("长银调额结果回调入参：{}", json);
        JSONObject resJson = new JSONObject(); //响应json
        resJson.put("status", "01");
        try {
            JSONObject jsonObject = JSONObject.parseObject(json);
            if (!hasKeyWithValue(jsonObject, "adjustNo")) {
                return CYBKCallBackQuotaResult.fail("长银调额流水号adjustNo不能为空");
            }
            if (!hasKeyWithValue(jsonObject, "outAdjustNo")) {
                return CYBKCallBackQuotaResult.fail("合作方调额流水号outAdjustNo不能为空");
            }

            //回调合作方调额流水号即为循环额度调额记录表主键id
            String adjustId = jsonObject.getString("outAdjustNo");
            String adjustNo = jsonObject.getString("adjustNo");
            //获取回调状态
            if (!hasKeyWithValue(jsonObject, "adjustStatus")) {
                return CYBKCallBackQuotaResult.fail("调额结果adjustStatus不能为空", adjustNo, adjustId);
            }
            ProcessStatus quotaCallBackResultStatus;
            String adjustStatus = jsonObject.getString("adjustStatus");
            switch (adjustStatus) {
                case "02":
                    quotaCallBackResultStatus = ProcessStatus.FAIL;
                    break;
                case "01":
                    quotaCallBackResultStatus = ProcessStatus.SUCCESS;
                    break;
                default:
                    getWarningService().warn("未知调额结果回调状态，循环额度调额记录表id：" + adjustId + "，adjustStatus：" + adjustStatus);
                    return CYBKCallBackQuotaResult.fail("未知调额结果回调状态：" + adjustStatus, adjustNo, adjustId);
            }

            String redisKey = "fin_adjustId_query_" + adjustId;
            Locker lock = lockService.getLock(redisKey);
            boolean locked;
            try {
                locked = lock.tryLock(Duration.ofSeconds(waitTime), Duration.ofSeconds(five));
                if (!locked) {
                    logger.warn("长银调额结果回调获取锁失败，adjustId：{}", adjustId);
                    return CYBKCallBackQuotaResult.fail("调额结果回调失败", adjustNo, adjustId);
                }
                return cybkQuotaService.doQuotaAdjustResultCallback(adjustId, adjustNo, quotaCallBackResultStatus);
            } finally {
                try {
                    lock.unlock();
                } catch (Exception e) {
                    logger.error("长银调额结果回调释放锁异常，adjustId：{}", adjustId, e);
                }
            }
        } catch (Exception e) {
            logger.error("长银调额结果回调失败：{}", e.getMessage(), e);
            return CYBKCallBackQuotaResult.fail("接收失败");
        }
    }

    /**
     * 还款结果回调
     *
     * @param json
     */
    @Override
    public String repayResultCallback(String json) {
        logger.info("长银还款结果回调入参：{}", json);
        try {
            JSONObject jsonObject = JSONObject.parseObject(json);
            //外部还款流水号即为对资还款记录表主键id
            if (!hasKeyWithValue(jsonObject, "outRepaySeq")) {
                return CYBKCallBackCommonResult.fail("外部还款流水号outRepaySeq不能为空");
            }
            String repayId = jsonObject.getString("outRepaySeq");
            //获取回调状态
            if (!hasKeyWithValue(jsonObject, "repayStatus")) {
                return CYBKCallBackCommonResult.fail("还款结果repayStatus不能为空");
            }
            ProcessStatus repayCallBackResultStatus;
            String repayStatus = jsonObject.getString("repayStatus");
            switch (repayStatus) {
                case "01":
                    repayCallBackResultStatus = ProcessStatus.SUCCESS;
                    break;
                case "02":
                    repayCallBackResultStatus = ProcessStatus.FAIL;
                    break;
                case "03":
                    return CYBKCallBackCommonResult.success();
                default:
                    getWarningService().warn("未知还款结果回调状态，对资还款记录表id：" + repayId + "，repayStatus：" + repayStatus);
                    return CYBKCallBackCommonResult.fail("未知还款结果回调状态：" + repayStatus);
            }
            String redisKey = "fin_repayId_query_" + repayId;
            Locker lock = lockService.getLock(redisKey);
            boolean locked;
            try {
                locked = lock.tryLock(Duration.ofSeconds(waitTime), Duration.ofSeconds(five));
                if (!locked) {
                    logger.warn("长银还款结果回调获取锁失败，repayId：{}", repayId);
                    return CYBKCallBackCommonResult.fail();
                }
                return cybkRepayService.doRepayResultCallback(repayId, repayCallBackResultStatus);
            } finally {
                try {
                    lock.unlock();
                } catch (Exception e) {
                    logger.error("长银还款结果回调释放锁异常，repayId：{}", repayId, e);
                }
            }
        } catch (Exception e) {
            logger.error("长银还款结果回调失败：{}", e.getMessage(), e);
            return CYBKCallBackCommonResult.fail();
        }
    }

    /**
     * 是否支持该资方渠道
     *
     * @param channel 资方渠道
     * @return 支持情况
     */
    @Override
    public boolean isSupport(BankChannel channel) {
        return BankChannel.CYBK == channel;
    }

    public WarningService getWarningService() {
        return warningService;
    }

    protected CommonService getCommonService() {
        return commonService;
    }
}

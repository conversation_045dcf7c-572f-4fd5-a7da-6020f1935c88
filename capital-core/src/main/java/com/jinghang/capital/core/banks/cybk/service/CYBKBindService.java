package com.jinghang.capital.core.banks.cybk.service;

import com.alibaba.fastjson2.JSONObject;
import com.jinghang.capital.core.banks.AbstractBankBindService;
import com.jinghang.capital.core.banks.cybk.config.CYBKConfig;
import com.jinghang.capital.core.banks.cybk.dto.bind.CYBKBindCardApplyRequest;
import com.jinghang.capital.core.banks.cybk.dto.bind.CYBKBindCardApplyResponse;
import com.jinghang.capital.core.banks.cybk.dto.bind.CYBKCardChangeRequest;
import com.jinghang.capital.core.banks.cybk.dto.bind.CYBKCardChangeResponse;
import com.jinghang.capital.core.banks.cybk.remote.CYBKRequestService;
import com.jinghang.capital.core.entity.AccountBankCard;
import com.jinghang.capital.core.entity.CYBKBankList;
import com.jinghang.capital.core.entity.CYBKCreditFlow;
import com.jinghang.capital.core.entity.Credit;
import com.jinghang.capital.core.entity.Loan;
import com.jinghang.capital.core.enums.BankChannel;
import com.jinghang.capital.core.enums.ProcessStatus;
import com.jinghang.capital.core.enums.SignStatus;
import com.jinghang.capital.core.exception.BizErrorCode;
import com.jinghang.capital.core.exception.BizException;
import com.jinghang.capital.core.repository.AccountBankCardRepository;
import com.jinghang.capital.core.repository.CYBKBankRepository;
import com.jinghang.capital.core.repository.CYBKCreditFlowRepository;
import com.jinghang.capital.core.repository.LoanRepository;
import com.jinghang.capital.core.util.IdGenUtil;
import com.jinghang.capital.core.vo.bind.BindApplyVo;
import com.jinghang.capital.core.vo.bind.BindResultVo;
import com.jinghang.common.util.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class CYBKBindService extends AbstractBankBindService {

    private static final Logger logger = LoggerFactory.getLogger(CYBKBindService.class);

    private static final String BIND_APPLY_SUCCESS_CODE = "1";
    private static final String CARD_CHANGE_SUCCESS_CODE = "0";

    @Autowired
    private CYBKConfig cybkConfig;
    @Autowired
    private CYBKBankRepository cybkBankRepository;
    @Autowired
    private CYBKRequestService requestService;
    @Autowired
    private LoanRepository loanRepository;
    @Autowired
    private CYBKCreditFlowRepository cybkCreditFlowRepository;
    @Autowired
    private AccountBankCardRepository accountBankCardRepository;

    /**
     * 长银直连银行卡签约申请
     *
     * @param accountBankCard 银行卡
     * @param credit
     * @param loan
     * @return
     */
    @Override
    protected BindResultVo bankBindApply(AccountBankCard accountBankCard, Credit credit, Loan loan, BindApplyVo applyVo) {
        BindResultVo resultVo = new BindResultVo();
        //贷前协议同步走放款逻辑, 不在这里同步
        if (StringUtil.isBlank(applyVo.getSysLoanId())) {
            logger.error("长银直连,绑卡协议同步失败:流程不支持.request:{}", JSONObject.toJSONString(applyVo));
            resultVo.setStatus(ProcessStatus.FAIL);
            resultVo.setSignStatus(SignStatus.N);
            resultVo.setMsg("绑卡失败");
            return resultVo;
        }
        CYBKBindCardApplyRequest request = new CYBKBindCardApplyRequest();
        request.setOutSignSeq(IdGenUtil.genReqNo("CG"));
        request.setPayMode("1"); //银行卡
        request.setSignNo(applyVo.getAgreementNo());
        request.setIdType("20"); //身份证
        request.setIdNo(accountBankCard.getCertNo());
        request.setTelNo(accountBankCard.getPhone());
        request.setAcctNo(accountBankCard.getCardNo());
        request.setAcctName(accountBankCard.getCardName());
        request.setPayChannel(cybkConfig.getRepayChannel(credit.getGuaranteeCompany()));
        request.setSignSts("00");

        CYBKBankList cybkBank = cybkBankRepository.findByAbbrIgnoreCase(accountBankCard.getBankCode());
        request.setAcctBankCode(cybkBank.getCycfcCode());

        CYBKBindCardApplyResponse response = requestService.bindApply(request, loan.getGuaranteeCompany());
        if (BIND_APPLY_SUCCESS_CODE.equals(response.getStatus())) {
            //协议同步成功后,调用卡号变更通知(不用调用)
            // return changeCardNotify(applyVo.getSysLoanId(), accountBankCard, applyVo.getAgreementNo());
            //更新还款卡信息
            loan.setRepayCardId(accountBankCard.getId());
            loanRepository.save(loan);
            resultVo.setStatus(ProcessStatus.SUCCESS);
            resultVo.setSignStatus(SignStatus.Y);
            resultVo.setSysId(accountBankCard.getId());
            resultVo.setAgreementNo(applyVo.getAgreementNo());
            resultVo.setMsg("银行卡签约申请成功");
            return resultVo;
        }

        logger.error("长银直连,绑卡协议同步失败:{}", response.getStatusDesc());
        resultVo.setStatus(ProcessStatus.FAIL);
        resultVo.setSignStatus(SignStatus.N);
        resultVo.setMsg(response.getStatusDesc());
        return resultVo;
    }

    private BindResultVo changeCardNotify(String outLoanId, AccountBankCard accountBankCard, String agreementNo) {
        BindResultVo resultVo = new BindResultVo();

        Loan loan = loanRepository.findByOuterLoanId(outLoanId).orElseThrow(() -> new BizException(BizErrorCode.LOAN_NOT_FOUND));
        CYBKCreditFlow creditFlow = cybkCreditFlowRepository.findByCreditId(loan.getCreditId()).orElseThrow();
        //变更前的还款卡信息
        AccountBankCard bankCardOld = accountBankCardRepository.findById(loan.getRepayCardId()).orElseThrow();

        CYBKBankList cybkBank = cybkBankRepository.findByAbbrIgnoreCase(accountBankCard.getBankCode());
        CYBKBankList cybkBankOld = cybkBankRepository.findByAbbrIgnoreCase(bankCardOld.getBankCode());

        CYBKCardChangeRequest request = new CYBKCardChangeRequest();
        request.setOutAcctChgSeq(IdGenUtil.genReqNo(null)); //需要纯数字字符串
        request.setApplCde(creditFlow.getCreditNo());
        request.setLoanNo(creditFlow.getLoanNo());
        request.setIdTyp("20");
        request.setIdNo(accountBankCard.getCertNo());
        request.setAcctName(accountBankCard.getCardName());
        request.setAcctKind("02"); //01 放款账号   02 还款账号
        request.setAcctTyp("01"); //个人账户
        request.setOldAcctBankCde(cybkBankOld.getCycfcCode());
        request.setOldAcctNo(bankCardOld.getCardNo());
        request.setOldAcctPhone(bankCardOld.getPhone());
        request.setNewAcctBankCde(cybkBank.getCycfcCode());
        request.setNewAcctNo(accountBankCard.getCardNo());
        request.setNewAcctPhone(accountBankCard.getPhone());
        //request.setAgrSeq();
        //request.setAcctChangeUrl();
        CYBKCardChangeResponse response = requestService.cardChangeNotify(request, loan.getGuaranteeCompany());

        if (!CARD_CHANGE_SUCCESS_CODE.equals(response.getStatus())) {
            logger.error("长银直连,卡号变更通知失败");
            resultVo.setStatus(ProcessStatus.FAIL);
            resultVo.setSignStatus(SignStatus.N);
            resultVo.setMsg(response.getStatusDesc());
            return resultVo;
        }
        //更新还款卡信息
        loan.setRepayCardId(accountBankCard.getId());
        loanRepository.save(loan);
        resultVo.setStatus(ProcessStatus.SUCCESS);
        resultVo.setSignStatus(SignStatus.Y);
        resultVo.setSysId(accountBankCard.getId());
        resultVo.setAgreementNo(agreementNo);
        resultVo.setMsg("银行卡签约申请成功");
        return resultVo;
    }

    @Override
    protected BindResultVo bankBindConfirm(AccountBankCard bankCard, String smsCode, Credit credit, Loan loan) {
        return null;
    }


    @Override
    public boolean isSupport(BankChannel channel) {
        return BankChannel.CYBK == channel;
    }


}

package com.jinghang.capital.core.banks.cybk.service;


import com.jinghang.capital.core.banks.BankPlanService;
import com.jinghang.capital.core.dto.BankProcessDTO;
import com.jinghang.capital.core.entity.LoanReplan;
import com.jinghang.capital.core.enums.BankChannel;
import com.jinghang.capital.core.repository.LoanReplanRepository;
import com.jinghang.capital.core.vo.repay.PlanItemVo;
import com.jinghang.capital.core.vo.repay.PlanSyncVo;
import com.jinghang.capital.core.vo.repay.PlanVo;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Service
public class CYBKPlanService implements BankPlanService {

    private static final Logger logger = LoggerFactory.getLogger(CYBKPlanService.class);

    private LoanReplanRepository loanReplanRepository;

    @Override
    public void planSync(PlanSyncVo planSyncVo) {

    }

    @Override
    public void planPush(String recId) {

    }

    @Override
    public void planDetailPush(PlanVo planVo) {
        List<LoanReplan> replans = loanReplanRepository.findByLoanIdOrderByPeriod(planVo.getLoanId());
        // 更新融担费
        replans.forEach(p -> {
            PlanItemVo itemVo = getPlanVoByPeriod(planVo.getPlanItems(), p.getPeriod());
            // 对客融担费
            p.setGuaranteeAmt(itemVo.getGuaranteeAmt());
            // 总金额
            p.setTotalAmt(p.getPrincipalAmt().add(p.getInterestAmt()).add(p.getGuaranteeAmt()));
        });

        loanReplanRepository.saveAll(replans);
    }

    @Override
    public void syncPlanToCapital(String loanId) {

    }

    @Override
    public void syncRePlanManual(BankProcessDTO bankProcessDTO) {

    }


    private PlanItemVo getPlanVoByPeriod(List<PlanItemVo> planItemVos, int period) {
        return planItemVos.stream().filter(p -> p.getPeriod() == period).findAny().orElseThrow();
    }

    @Autowired
    public void setLoanReplanRepository(LoanReplanRepository loanReplanRepository) {
        this.loanReplanRepository = loanReplanRepository;
    }

    @Override
    public boolean isSupport(BankChannel channel) {
        return BankChannel.CYBK == channel;
    }
}

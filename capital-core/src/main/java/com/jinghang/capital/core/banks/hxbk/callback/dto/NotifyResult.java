package com.jinghang.capital.core.banks.hxbk.callback.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * HXBK回调响应结果数据
 * 用于构建回调接口的响应数据
 *
 * @Author: Lior
 * @CreateTime: 2025/7/9
 */
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class NotifyResult {

    /**
     * 是否成功
     * Y: 成功; N: 失败
     */
    @JsonProperty("success")
    private String success;

    public String getSuccess() {
        return success;
    }

    public void setSuccess(String success) {
        this.success = success;
    }

    /**
     * 创建成功响应
     *
     * @return NotifyResult
     */
    public static NotifyResult success() {
        NotifyResult result = new NotifyResult();
        result.setSuccess("Y");
        return result;
    }

    /**
     * 创建失败响应
     *
     * @return NotifyResult
     */
    public static NotifyResult failure() {
        NotifyResult result = new NotifyResult();
        result.setSuccess("N");
        return result;
    }
}

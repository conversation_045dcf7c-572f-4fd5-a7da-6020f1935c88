package com.jinghang.capital.core.banks.hxbk.util.crypto;

import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.Signature;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import javax.crypto.Cipher;
import org.apache.commons.codec.binary.Base64;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * RSA加解密SecretKey密钥和SHA256WithRSA加签验签工具类
 * 使用2048位RSA非对称加密算法和SHA256WithRSA签名算法
 *
 * @Author: Lior
 * @CreateTime: 2025/7/8 20:00
 */
public class RSAUtils {

    private static final Logger logger = LoggerFactory.getLogger(RSAUtils.class);

    private static final String RSA_ALGORITHM = "RSA";
    private static final String SIGNATURE_ALGORITHM = "SHA256WithRSA";

    /**
     * 使用RSA算法加密SecretKey密钥
     *
     * @param secretKey    待加密的SecretKey
     * @param publicKeyStr 公钥字符串
     * @return 加密后的SecretKey（Base64编码）
     */
    public static String encryptSecretKeyWithRSA(String secretKey, String publicKeyStr) {
        try {
            PublicKey publicKey = loadPublicKey(publicKeyStr);
            Cipher rsaCipher = Cipher.getInstance(RSA_ALGORITHM);
            rsaCipher.init(Cipher.ENCRYPT_MODE, publicKey);
            byte[] encryptedKeyBytes = rsaCipher.doFinal(secretKey.getBytes());

            String encryptedKey = Base64.encodeBase64String(encryptedKeyBytes);
            logger.debug("RSA加密SecretKey成功，原文长度: {}, 密文长度: {}", secretKey.length(), encryptedKey.length());
            return encryptedKey;
        } catch (Exception e) {
            logger.error("RSA加密SecretKey失败", e);
            throw new RuntimeException("RSA加密SecretKey失败", e);
        }
    }

    /**
     * 使用RSA算法解密SecretKey密钥
     *
     * @param encryptedSecretKey 加密的SecretKey
     * @param privateKeyStr      私钥字符串
     * @return 解密后的SecretKey明文
     */
    public static String decryptSecretKeyWithRSA(String encryptedSecretKey, String privateKeyStr) {
        try {
            PrivateKey privateKey = loadPrivateKey(privateKeyStr);
            Cipher rsaCipher = Cipher.getInstance(RSA_ALGORITHM);
            rsaCipher.init(Cipher.DECRYPT_MODE, privateKey);
            byte[] encryptedKeyBytes = Base64.decodeBase64(encryptedSecretKey);
            byte[] decryptedKeyBytes = rsaCipher.doFinal(encryptedKeyBytes);

            String decryptedKey = new String(decryptedKeyBytes);
            logger.debug("RSA解密SecretKey成功，密文长度: {}, 明文长度: {}", encryptedSecretKey.length(), decryptedKey.length());
            return decryptedKey;
        } catch (Exception e) {
            logger.error("RSA解密SecretKey失败", e);
            throw new RuntimeException("RSA解密SecretKey失败", e);
        }
    }

    /**
     * 使用SHA256WithRSA算法签名
     *
     * @param dataToSign    待签名数据
     * @param privateKeyStr 私钥字符串
     * @return 签名结果（Base64编码）
     */
    public static String signData(String dataToSign, String privateKeyStr) {
        try {
            PrivateKey privateKey = loadPrivateKey(privateKeyStr);
            Signature signature = Signature.getInstance(SIGNATURE_ALGORITHM);
            signature.initSign(privateKey);
            signature.update(dataToSign.getBytes());
            byte[] signatureBytes = signature.sign();

            String signResult = Base64.encodeBase64String(signatureBytes);
            logger.debug("SHA256WithRSA签名成功，原文长度: {}, 签名长度: {}", dataToSign.length(), signResult.length());
            return signResult;
        } catch (Exception e) {
            logger.error("SHA256WithRSA签名失败", e);
            throw new RuntimeException("SHA256WithRSA签名失败", e);
        }
    }

    /**
     * 使用SHA256WithRSA算法验证签名
     *
     * @param signedData   签名数据
     * @param dataToVerify 待验证数据
     * @param publicKeyStr 公钥字符串
     * @return 验签结果
     */
    public static boolean verifyData(String signedData, String dataToVerify, String publicKeyStr) {
        try {
            PublicKey publicKey = loadPublicKey(publicKeyStr);
            Signature signature = Signature.getInstance(SIGNATURE_ALGORITHM);
            signature.initVerify(publicKey);
            signature.update(dataToVerify.getBytes());
            boolean verified = signature.verify(Base64.decodeBase64(signedData));

            logger.debug("SHA256WithRSA验签结果: {}, 原文长度: {}, 签名长度: {}",
                    verified, dataToVerify.length(), signedData.length());
            return verified;
        } catch (Exception e) {
            logger.error("SHA256WithRSA验签失败", e);
            return false;
        }
    }

    /**
     * 加载公钥
     *
     * @param key 公钥字符串（Base64编码）
     * @return 公钥对象
     * @throws Exception 加载异常
     */
    private static PublicKey loadPublicKey(String key) throws Exception {
        byte[] keyBytes = java.util.Base64.getDecoder().decode(key);
        X509EncodedKeySpec keySpec = new X509EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance(RSA_ALGORITHM);
        return keyFactory.generatePublic(keySpec);
    }

    /**
     * 加载私钥
     *
     * @param key 私钥字符串（Base64编码）
     * @return 私钥对象
     * @throws Exception 加载异常
     */
    private static PrivateKey loadPrivateKey(String key) throws Exception {
        byte[] keyBytes = java.util.Base64.getDecoder().decode(key);
        PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance(RSA_ALGORITHM);
        return keyFactory.generatePrivate(keySpec);
    }
}

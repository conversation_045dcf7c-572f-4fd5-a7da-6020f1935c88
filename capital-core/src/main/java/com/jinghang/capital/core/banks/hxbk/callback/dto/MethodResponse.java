package com.jinghang.capital.core.banks.hxbk.callback.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * HXBK授信结果回调响应数据
 *
 * @Author: Lior
 * @CreateTime: 2025/7/9 14:51
 */
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class MethodResponse {

    /**
     * 响应码
     * 000000成功
     * 999999失败
     */
    @JsonProperty("recode")
    private String recode;

    /**
     * 响应消息
     */
    @JsonProperty("remsg")
    private String remsg;

    /**
     * 返回具体值
     */
    @JsonProperty("data")
    private NotifyResult data;

    public String getRecode() {
        return recode;
    }

    public void setRecode(String recode) {
        this.recode = recode;
    }

    public String getRemsg() {
        return remsg;
    }

    public void setRemsg(String remsg) {
        this.remsg = remsg;
    }

    public NotifyResult getData() {
        return data;
    }

    public void setData(NotifyResult data) {
        this.data = data;
    }

    /**
     * 创建成功响应
     *
     * @param message 响应消息
     * @return CreditResultCallbackResponse
     */
    public static MethodResponse success(String message) {
        MethodResponse response = new MethodResponse();
        response.setRecode("000000");
        response.setRemsg(message);
        response.setData(NotifyResult.success());
        return response;
    }

    /**
     * 创建成功响应（默认消息）
     *
     * @return CreditResultCallbackResponse
     */
    public static MethodResponse success() {
        return success("处理成功");
    }

    /**
     * 创建失败响应
     *
     * @param message 响应消息
     * @return CreditResultCallbackResponse
     */
    public static MethodResponse failure(String message) {
        MethodResponse response = new MethodResponse();
        response.setRecode("999999");
        response.setRemsg(message);
        response.setData(NotifyResult.failure());
        return response;
    }

    /**
     * 创建失败响应（默认消息）
     *
     * @return CreditResultCallbackResponse
     */
    public static MethodResponse failure() {
        return failure("处理失败");
    }
}

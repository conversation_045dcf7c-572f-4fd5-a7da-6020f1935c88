package com.jinghang.capital.core.banks.hxbk.dto.repay;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.jinghang.capital.core.banks.hxbk.dto.HXBKBaseRequest;
import com.jinghang.capital.core.banks.hxbk.enums.HXBKMethod;

@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class HXBKRepayQueryRequest extends HXBKBaseRequest {
    @JsonIgnore
    private static final HXBKMethod REPAY_QUERY = HXBKMethod.REPAY_QUERY;

    /**
     * 订单号
     */
    @JsonProperty("order_no")
    private String orderNo;
    /**
     * 原还款订单号
     */
    @JsonProperty("original_order_no")
    private String originalOrderNo;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getOriginalOrderNo() {
        return originalOrderNo;
    }

    public void setOriginalOrderNo(String originalOrderNo) {
        this.originalOrderNo = originalOrderNo;
    }

    @Override
    public HXBKMethod getMethod() {
        return REPAY_QUERY;
    }
}

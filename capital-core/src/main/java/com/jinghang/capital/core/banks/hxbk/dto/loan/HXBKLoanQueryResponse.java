package com.jinghang.capital.core.banks.hxbk.dto.loan;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;

@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class HXBKLoanQueryResponse {

    //用信状态 STRING Y 0-通过 1-不通过 2-处理中
    @JsonProperty("status")
    private String status;

    //拒绝原因 STRING N 失败时返回
    @JsonProperty("msg")
    private String msg;


    //借据信息 ReceiptInfo N 成功时返回
    @JsonProperty("receipt_info")
    private ReceiptInfoBean receiptInfo;

    //还款计划列表 ARRAY[Repay] N 成功时返回
    @JsonProperty("repay_ref")
    private List<HXBKRepayList> repayRef;


    //用信合同编号 STRING N
    @JsonProperty("disburse_contract_no")
    private String disburseContractNo;

    //授信合同编号 STRING N
    @JsonProperty("credit_contract_no")
    private String creditContractNo;


    @JsonProperty("result_code")
    private String resultCode;


    //结果信息 STRING Y
    @JsonProperty("result_msg")
    private String resultMsg;

    //请求唯一id STRING Y
    @JsonProperty("req_msg_id")
    private String reqMsgId;


    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public ReceiptInfoBean getReceiptInfo() {
        return receiptInfo;
    }

    public void setReceiptInfo(ReceiptInfoBean receiptInfo) {
        this.receiptInfo = receiptInfo;
    }

    public List<HXBKRepayList> getRepayRef() {
        return repayRef;
    }

    public void setRepayRef(List<HXBKRepayList> repayRef) {
        this.repayRef = repayRef;
    }

    public String getDisburseContractNo() {
        return disburseContractNo;
    }

    public void setDisburseContractNo(String disburseContractNo) {
        this.disburseContractNo = disburseContractNo;
    }

    public String getCreditContractNo() {
        return creditContractNo;
    }

    public void setCreditContractNo(String creditContractNo) {
        this.creditContractNo = creditContractNo;
    }

    public String getResultCode() {
        return resultCode;
    }

    public void setResultCode(String resultCode) {
        this.resultCode = resultCode;
    }

    public String getResultMsg() {
        return resultMsg;
    }

    public void setResultMsg(String resultMsg) {
        this.resultMsg = resultMsg;
    }

    public String getReqMsgId() {
        return reqMsgId;
    }

    public void setReqMsgId(String reqMsgId) {
        this.reqMsgId = reqMsgId;
    }
}

package com.jinghang.capital.core.banks.hxbk.dto.loan;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.jinghang.capital.core.banks.hxbk.config.FlexibleLocalDateTimeDeserializer;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025-07-10 14:40
 */
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@JsonIgnoreProperties(ignoreUnknown = true)
public class HXBKRepayList {
    @JsonProperty("custom_no")
    private String customNo; // 客户编码

    @JsonProperty("period")
    private String period; // 当前期数

    @JsonProperty("need_amount")
    private BigDecimal needAmount; // 应还总额

    @JsonProperty("need_corpus")
    private BigDecimal needCorpus; // 应还本金

    @JsonProperty("need_accrual")
    private BigDecimal needAccrual; // 应还利息

    @JsonProperty("need_service_charge")
    private BigDecimal needServiceCharge; // 应还服务费

    @JsonProperty("need_fee")
    private BigDecimal needFee; // 应还手续费(罚息)

    @JsonProperty("already_amount")
    private BigDecimal alreadyAmount; // 已还总额

    @JsonProperty("already_corpus")
    private BigDecimal alreadyCorpus; // 已还本金

    @JsonProperty("already_overcorpus")
    private BigDecimal alreadyOvercorpus; // 已还逾期本金

    @JsonProperty("already_accrual")
    private BigDecimal alreadyAccrual; // 已还利息

    @JsonProperty("already_service_charge")
    private BigDecimal alreadyServiceCharge; // 已还服务费

    @JsonProperty("already_punish")
    private BigDecimal alreadyPunish; // 已还逾期息(罚息)

    @JsonProperty("already_fee")
    private BigDecimal alreadyFee; // 已还手续费（废弃）

    @JsonProperty("rate")
    private BigDecimal rate; // 利率

    @JsonProperty("penalty_value")
    private BigDecimal penaltyValue; // 罚息率

    @JsonProperty("rest_amount")
    private BigDecimal restAmount; // 当期剩余总额

    @JsonProperty("rest_corpus")
    private BigDecimal restCorpus; // 当期剩余本金

    @JsonProperty("rest_accrual")
    private BigDecimal restAccrual; // 当期剩余利息

    @JsonProperty("rest_punish")
    private BigDecimal restPunish; // 当期剩余罚息

    @JsonProperty("rest_service_charge")
    private BigDecimal restServiceCharge; // 当期剩余服务费

    @JsonProperty("remain_corpus")
    private BigDecimal remainCorpus; // 期末本金

    @JsonProperty("receipt_no")
    private String receiptNo; // 借据编号

    @JsonProperty("status")
    private String status; // 还款状态 1：已还清；2：未还；3：部分还款4：逾期

    @JsonProperty("overdue_days")
    private BigDecimal overdueDays; // 逾期天数

    @JsonDeserialize(using = FlexibleLocalDateTimeDeserializer.class)
    @JsonProperty("settle_date")
    private LocalDateTime settleDate; // 应还日期

    @JsonDeserialize(using = FlexibleLocalDateTimeDeserializer.class)
    @JsonProperty("trade_date")
    private LocalDateTime tradeDate; // 还款日期

    @JsonDeserialize(using = FlexibleLocalDateTimeDeserializer.class)
    @JsonProperty("start_time")
    private LocalDateTime startTime; // 当期起始时间

    @JsonDeserialize(using = FlexibleLocalDateTimeDeserializer.class)
    @JsonProperty("start_time_sup")
    private LocalDateTime startTimeSup; // 当期起始时间 兼容字段 yyyy-MM-dd

    @JsonDeserialize(using = FlexibleLocalDateTimeDeserializer.class)
    @JsonProperty("end_time")
    private LocalDateTime endTime; // 当期结束时间

    @JsonDeserialize(using = FlexibleLocalDateTimeDeserializer.class)
    @JsonProperty("end_time_sup")
    private LocalDateTime endTimeSup; // 当期结束时间 兼容字段 yyyy-MM-dd

    @JsonProperty("already_guarantee_fee")
    private BigDecimal alreadyGuaranteeFee; // 已还担保费

    @JsonProperty("already_liquidated_damages")
    private BigDecimal alreadyLiquidatedDamages; // 已还违约金

    @JsonProperty("rest_guarantee_fee")
    private BigDecimal restGuaranteeFee; // 当期剩余担保费

    @JsonProperty("rest_liquidated_damages")
    private BigDecimal restLiquidatedDamages; // 当期剩余违约金

    @JsonProperty("need_guarantee_fee")
    private BigDecimal needGuaranteeFee; // 应还担保费

    @JsonProperty("need_liquidated_damages")
    private BigDecimal needLiquidatedDamages; // 应还违约金

    public String getCustomNo() {
        return customNo;
    }

    public void setCustomNo(String customNo) {
        this.customNo = customNo;
    }

    public String getPeriod() {
        return period;
    }

    public void setPeriod(String period) {
        this.period = period;
    }

    public BigDecimal getNeedAmount() {
        return needAmount;
    }

    public void setNeedAmount(BigDecimal needAmount) {
        this.needAmount = needAmount;
    }

    public BigDecimal getNeedCorpus() {
        return needCorpus;
    }

    public void setNeedCorpus(BigDecimal needCorpus) {
        this.needCorpus = needCorpus;
    }

    public BigDecimal getNeedAccrual() {
        return needAccrual;
    }

    public void setNeedAccrual(BigDecimal needAccrual) {
        this.needAccrual = needAccrual;
    }

    public BigDecimal getNeedServiceCharge() {
        return needServiceCharge;
    }

    public void setNeedServiceCharge(BigDecimal needServiceCharge) {
        this.needServiceCharge = needServiceCharge;
    }

    public BigDecimal getNeedFee() {
        return needFee;
    }

    public void setNeedFee(BigDecimal needFee) {
        this.needFee = needFee;
    }

    public BigDecimal getAlreadyAmount() {
        return alreadyAmount;
    }

    public void setAlreadyAmount(BigDecimal alreadyAmount) {
        this.alreadyAmount = alreadyAmount;
    }

    public BigDecimal getAlreadyCorpus() {
        return alreadyCorpus;
    }

    public void setAlreadyCorpus(BigDecimal alreadyCorpus) {
        this.alreadyCorpus = alreadyCorpus;
    }

    public BigDecimal getAlreadyOvercorpus() {
        return alreadyOvercorpus;
    }

    public void setAlreadyOvercorpus(BigDecimal alreadyOvercorpus) {
        this.alreadyOvercorpus = alreadyOvercorpus;
    }

    public BigDecimal getAlreadyAccrual() {
        return alreadyAccrual;
    }

    public void setAlreadyAccrual(BigDecimal alreadyAccrual) {
        this.alreadyAccrual = alreadyAccrual;
    }

    public BigDecimal getAlreadyServiceCharge() {
        return alreadyServiceCharge;
    }

    public void setAlreadyServiceCharge(BigDecimal alreadyServiceCharge) {
        this.alreadyServiceCharge = alreadyServiceCharge;
    }

    public BigDecimal getAlreadyPunish() {
        return alreadyPunish;
    }

    public void setAlreadyPunish(BigDecimal alreadyPunish) {
        this.alreadyPunish = alreadyPunish;
    }

    public BigDecimal getAlreadyFee() {
        return alreadyFee;
    }

    public void setAlreadyFee(BigDecimal alreadyFee) {
        this.alreadyFee = alreadyFee;
    }

    public BigDecimal getRate() {
        return rate;
    }

    public void setRate(BigDecimal rate) {
        this.rate = rate;
    }

    public BigDecimal getPenaltyValue() {
        return penaltyValue;
    }

    public void setPenaltyValue(BigDecimal penaltyValue) {
        this.penaltyValue = penaltyValue;
    }

    public BigDecimal getRestAmount() {
        return restAmount;
    }

    public void setRestAmount(BigDecimal restAmount) {
        this.restAmount = restAmount;
    }

    public BigDecimal getRestCorpus() {
        return restCorpus;
    }

    public void setRestCorpus(BigDecimal restCorpus) {
        this.restCorpus = restCorpus;
    }

    public BigDecimal getRestAccrual() {
        return restAccrual;
    }

    public void setRestAccrual(BigDecimal restAccrual) {
        this.restAccrual = restAccrual;
    }

    public BigDecimal getRestPunish() {
        return restPunish;
    }

    public void setRestPunish(BigDecimal restPunish) {
        this.restPunish = restPunish;
    }

    public BigDecimal getRestServiceCharge() {
        return restServiceCharge;
    }

    public void setRestServiceCharge(BigDecimal restServiceCharge) {
        this.restServiceCharge = restServiceCharge;
    }

    public BigDecimal getRemainCorpus() {
        return remainCorpus;
    }

    public void setRemainCorpus(BigDecimal remainCorpus) {
        this.remainCorpus = remainCorpus;
    }

    public String getReceiptNo() {
        return receiptNo;
    }

    public void setReceiptNo(String receiptNo) {
        this.receiptNo = receiptNo;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public BigDecimal getOverdueDays() {
        return overdueDays;
    }

    public void setOverdueDays(BigDecimal overdueDays) {
        this.overdueDays = overdueDays;
    }

    public LocalDateTime getSettleDate() {
        return settleDate;
    }

    public void setSettleDate(LocalDateTime settleDate) {
        this.settleDate = settleDate;
    }

    public LocalDateTime getTradeDate() {
        return tradeDate;
    }

    public void setTradeDate(LocalDateTime tradeDate) {
        this.tradeDate = tradeDate;
    }

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }

    public LocalDateTime getStartTimeSup() {
        return startTimeSup;
    }

    public void setStartTimeSup(LocalDateTime startTimeSup) {
        this.startTimeSup = startTimeSup;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }

    public LocalDateTime getEndTimeSup() {
        return endTimeSup;
    }

    public void setEndTimeSup(LocalDateTime endTimeSup) {
        this.endTimeSup = endTimeSup;
    }

    public BigDecimal getAlreadyGuaranteeFee() {
        return alreadyGuaranteeFee;
    }

    public void setAlreadyGuaranteeFee(BigDecimal alreadyGuaranteeFee) {
        this.alreadyGuaranteeFee = alreadyGuaranteeFee;
    }

    public BigDecimal getAlreadyLiquidatedDamages() {
        return alreadyLiquidatedDamages;
    }

    public void setAlreadyLiquidatedDamages(BigDecimal alreadyLiquidatedDamages) {
        this.alreadyLiquidatedDamages = alreadyLiquidatedDamages;
    }

    public BigDecimal getRestGuaranteeFee() {
        return restGuaranteeFee;
    }

    public void setRestGuaranteeFee(BigDecimal restGuaranteeFee) {
        this.restGuaranteeFee = restGuaranteeFee;
    }

    public BigDecimal getRestLiquidatedDamages() {
        return restLiquidatedDamages;
    }

    public void setRestLiquidatedDamages(BigDecimal restLiquidatedDamages) {
        this.restLiquidatedDamages = restLiquidatedDamages;
    }

    public BigDecimal getNeedGuaranteeFee() {
        return needGuaranteeFee;
    }

    public void setNeedGuaranteeFee(BigDecimal needGuaranteeFee) {
        this.needGuaranteeFee = needGuaranteeFee;
    }

    public BigDecimal getNeedLiquidatedDamages() {
        return needLiquidatedDamages;
    }

    public void setNeedLiquidatedDamages(BigDecimal needLiquidatedDamages) {
        this.needLiquidatedDamages = needLiquidatedDamages;
    }
}


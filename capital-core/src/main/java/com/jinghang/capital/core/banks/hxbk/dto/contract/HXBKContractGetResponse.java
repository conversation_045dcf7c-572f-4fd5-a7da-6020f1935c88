package com.jinghang.capital.core.banks.hxbk.dto.contract;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;

/**
 * @作者 Mr.sandman
 * @时间 2025/07/12 11:13
 */
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class HXBKContractGetResponse {

    private List<Contract> contracts;

    /**
     * 结果编码（详见附录）
     */
    @JsonProperty("result_code")
    private String resultCode;

    /**
     * 结果信息
     */
    @JsonProperty("result_msg")
    private String resultMsg;

    /**
     * 请求唯一ID
     */
    @JsonProperty("req_msg_id")
    private String reqMsgId;

    public List<Contract> getContracts() {
        return contracts;
    }

    public void setContracts(List<Contract> contracts) {
        this.contracts = contracts;
    }

    public String getResultCode() {
        return resultCode;
    }

    public void setResultCode(String resultCode) {
        this.resultCode = resultCode;
    }

    public String getResultMsg() {
        return resultMsg;
    }

    public void setResultMsg(String resultMsg) {
        this.resultMsg = resultMsg;
    }

    public String getReqMsgId() {
        return reqMsgId;
    }

    public void setReqMsgId(String reqMsgId) {
        this.reqMsgId = reqMsgId;
    }
}

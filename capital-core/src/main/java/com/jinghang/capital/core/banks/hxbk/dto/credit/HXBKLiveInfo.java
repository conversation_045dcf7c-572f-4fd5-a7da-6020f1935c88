package com.jinghang.capital.core.banks.hxbk.dto.credit;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * HXBK居住信息
 *
 * @Author: Lior
 * @CreateTime: 2025/7/8 10:15
 */
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class HXBKLiveInfo {

    /**
     * 居住省份 编码
     * 用户自填
     */
    @JsonProperty("live_province")
    private String liveProvince;

    /**
     * 居住城市 编码
     * 用户自填
     */
    @JsonProperty("live_city")
    private String liveCity;

    /**
     * 居住区域 编码
     * 用户自填
     */
    @JsonProperty("live_area")
    private String liveArea;

    /**
     * 居住街道
     * 用户自填
     */
    @JsonProperty("live_street")
    private String liveStreet;

    /**
     * 居住详细地址
     * 用户自填
     */
    @JsonProperty("live_address")
    private String liveAddress;

    /**
     * 居住年限
     * 空
     */
    @JsonProperty("live_years")
    private Integer liveYears;

    // Getter and Setter methods
    public String getLiveProvince() {
        return liveProvince;
    }

    public void setLiveProvince(String liveProvince) {
        this.liveProvince = liveProvince;
    }

    public String getLiveCity() {
        return liveCity;
    }

    public void setLiveCity(String liveCity) {
        this.liveCity = liveCity;
    }

    public String getLiveArea() {
        return liveArea;
    }

    public void setLiveArea(String liveArea) {
        this.liveArea = liveArea;
    }

    public String getLiveStreet() {
        return liveStreet;
    }

    public void setLiveStreet(String liveStreet) {
        this.liveStreet = liveStreet;
    }

    public String getLiveAddress() {
        return liveAddress;
    }

    public void setLiveAddress(String liveAddress) {
        this.liveAddress = liveAddress;
    }

    public Integer getLiveYears() {
        return liveYears;
    }

    public void setLiveYears(Integer liveYears) {
        this.liveYears = liveYears;
    }
}

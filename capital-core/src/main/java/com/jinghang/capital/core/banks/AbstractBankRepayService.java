package com.jinghang.capital.core.banks;

import com.jinghang.capital.core.convert.entity.RepayConvert;
import com.jinghang.capital.core.dto.BankProcessDTO;
import com.jinghang.capital.core.entity.BankBatchSubstituteRecord;
import com.jinghang.capital.core.entity.BankConfig;
import com.jinghang.capital.core.entity.BankLoanReplan;
import com.jinghang.capital.core.entity.BankRepayRecord;
import com.jinghang.capital.core.entity.BatchCustomerRepayDetail;
import com.jinghang.capital.core.entity.BatchCustomerRepayRecord;
import com.jinghang.capital.core.entity.ClaimAfterRepayRecord;
import com.jinghang.capital.core.entity.CompensatedRepayNotify;
import com.jinghang.capital.core.entity.CompensatedRepayNotifyDetail;
import com.jinghang.capital.core.entity.CustomerLoanReplan;
import com.jinghang.capital.core.entity.CustomerRepayRecord;
import com.jinghang.capital.core.entity.FlowClaimRecord;
import com.jinghang.capital.core.entity.Loan;
import com.jinghang.capital.core.entity.LoanReplan;
import com.jinghang.capital.core.enums.BankChannel;
import com.jinghang.capital.core.enums.LoanStage;
import com.jinghang.capital.core.enums.Payee;
import com.jinghang.capital.core.enums.ProcessStatus;
import com.jinghang.capital.core.enums.PushStatus;
import com.jinghang.capital.core.enums.RepayMode;
import com.jinghang.capital.core.enums.RepayPurpose;
import com.jinghang.capital.core.enums.RepayStatus;
import com.jinghang.capital.core.enums.RepayType;
import com.jinghang.capital.core.enums.WhetherState;
import com.jinghang.capital.core.exception.BizErrorCode;
import com.jinghang.capital.core.exception.BizException;
import com.jinghang.capital.core.service.CommonService;
import com.jinghang.capital.core.service.MqService;
import com.jinghang.capital.core.service.WarningService;
import com.jinghang.capital.core.service.repay.FinRepayService;
import com.jinghang.capital.core.util.AmountUtil;
import com.jinghang.capital.core.vo.recc.RccConfirmVo;
import com.jinghang.capital.core.vo.recc.ReccDetailQueryResultVo;
import com.jinghang.capital.core.vo.recc.ReccDetailQueryVo;
import com.jinghang.capital.core.vo.recc.ReccResultVo;
import com.jinghang.capital.core.vo.repay.ActiveClaimApplyResultVo;
import com.jinghang.capital.core.vo.repay.ActiveClaimApplyVo;
import com.jinghang.capital.core.vo.repay.ActiveClaimQueryResultVo;
import com.jinghang.capital.core.vo.repay.ActiveClaimQueryVo;
import com.jinghang.capital.core.vo.repay.ActiveLaunchClaimApplyVo;
import com.jinghang.capital.core.vo.repay.ActiveLaunchClaimResultVo;
import com.jinghang.capital.core.vo.repay.BankPlanItemVo;
import com.jinghang.capital.core.vo.repay.BankPlanQueryRltVo;
import com.jinghang.capital.core.vo.repay.BankPlanQueryVo;
import com.jinghang.capital.core.vo.repay.BankRepayRecordVo;
import com.jinghang.capital.core.vo.repay.BatchTrailVo;
import com.jinghang.capital.core.vo.repay.BatchTrialResultVo;
import com.jinghang.capital.core.vo.repay.ClaimMarkApplyVo;
import com.jinghang.capital.core.vo.repay.ClaimMarkResultVo;
import com.jinghang.capital.core.vo.repay.ClaimRetryResultVo;
import com.jinghang.capital.core.vo.repay.ClaimRetryVo;
import com.jinghang.capital.core.vo.repay.CompensatedRepaySyncRlt;
import com.jinghang.capital.core.vo.repay.CompensatedRepaySyncVo;
import com.jinghang.capital.core.vo.repay.DefrayResultVo;
import com.jinghang.capital.core.vo.repay.DefrayVo;
import com.jinghang.capital.core.vo.repay.LaunchClaimVo;
import com.jinghang.capital.core.vo.repay.OverduePlanQueryVo;
import com.jinghang.capital.core.vo.repay.OverduePlanVo;
import com.jinghang.capital.core.vo.repay.PlanItemVo;
import com.jinghang.capital.core.vo.repay.PlanOverdueVo;
import com.jinghang.capital.core.vo.repay.PlanQueryVo;
import com.jinghang.capital.core.vo.repay.PlanVo;
import com.jinghang.capital.core.vo.repay.RepayApplyVo;
import com.jinghang.capital.core.vo.repay.RepayDeductionApplyVo;
import com.jinghang.capital.core.vo.repay.RepayNoticeResultVo;
import com.jinghang.capital.core.vo.repay.RepayNoticeVo;
import com.jinghang.capital.core.vo.repay.RepayResultVo;
import com.jinghang.capital.core.vo.repay.RepayTrailVo;
import com.jinghang.capital.core.vo.repay.SubstituteApplyResultVo;
import com.jinghang.capital.core.vo.repay.SubstituteApplyVo;
import com.jinghang.capital.core.vo.repay.SubstituteMarkApplyVo;
import com.jinghang.capital.core.vo.repay.SubstituteMarkResultVo;
import com.jinghang.capital.core.vo.repay.TrailResultVo;
import com.jinghang.capital.core.vo.repay.TrustPlanReleaseVo;
import com.jinghang.common.util.CollectionUtil;
import com.jinghang.common.util.JsonUtil;
import com.jinghang.common.util.StringUtil;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2023/5/20
 */
public abstract class AbstractBankRepayService implements BankRepayService {
    public static final DateTimeFormatter VALID_FORMAT = DateTimeFormatter.ofPattern("HHmm");
    //一年按照360日
    protected static final BigDecimal YEAR_DAY_360 = new BigDecimal("360");
    //一年按照365日
    protected static final BigDecimal YEAR_DAY_365 = new BigDecimal("365");
    protected static final Integer NOT_ALLOW_PERIOD = 3;
    private static final Logger logger = LoggerFactory.getLogger(AbstractBankRepayService.class);
    private static final int NUM_FIVE = 5;
    @Autowired
    private MqService mqService;
    @Autowired
    private FinRepayService finRepayService;
    @Autowired
    private CommonService commonService;
    private WarningService warningService;
    private InnerClaimMarkService innerClaimMarkService;

    public static BigDecimal sumAmount(BigDecimal... amount) {
        return Arrays.stream(amount).map(i -> Objects.requireNonNullElse(i, BigDecimal.ZERO)).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    @Override
    public RepayResultVo apply(CustomerRepayRecord customerRepayRecord, RepayApplyVo repayApplyDto) {

        Integer period = customerRepayRecord.getPeriod();
        LoanReplan repayPlan;
        if (period == null) { // 结清
            List<LoanReplan> repayPlans = finRepayService.getRepayPlans(customerRepayRecord.getLoanId());
            LoanReplan customMinPlan = repayPlans.stream().filter(p -> p.getCustRepayStatus() == RepayStatus.NORMAL)
                    .min(Comparator.comparing(LoanReplan::getPeriod)).orElseThrow(() -> new BizException(BizErrorCode.REPAY_INVALID));
            LoanReplan bankMinPlan = repayPlans.stream().filter(p -> p.getBankRepayStatus() == RepayStatus.NORMAL)
                    .min(Comparator.comparing(LoanReplan::getPeriod)).orElseThrow(() -> new BizException(BizErrorCode.REPAY_INVALID));
            if (customMinPlan != bankMinPlan) {
                throw new BizException(BizErrorCode.REPAY_INVALID);
            }
            repayPlan = customMinPlan;
        } else {
            repayPlan = finRepayService.getRepayPlan(customerRepayRecord.getLoanId(), period);
        }

        RepayMode repayMode = repayApplyDto.getRepayMode();
        if (repayMode == RepayMode.OFFLINE) {
            return offlineRepay(customerRepayRecord, repayPlan, repayApplyDto);
        }

        return onlineRepay(customerRepayRecord, repayPlan, repayApplyDto);
    }

    /**
     * 代偿后还款试算 无需按日计息 取还款计划金额
     *
     * @return
     */
    protected TrailResultVo claimAfterTrailByPlan(RepayTrailVo repayTrailVo) {
        LoanReplan currentRepayPlan = finRepayService.getRepayPlan(repayTrailVo.getLoanId(), repayTrailVo.getPeriod());

        // 代偿后还当期
        if (RepayPurpose.CURRENT.equals(repayTrailVo.getRepayPurpose())) {
            TrailResultVo trailResultVo = RepayConvert.INSTANCE.loanReplanToTrailResultVo(currentRepayPlan);
            trailResultVo.setPayee(Payee.GUARANTEE);
            return trailResultVo;
        }

        // 代偿后结清
        List<LoanReplan> repayPlans = finRepayService.getRepayPlans(repayTrailVo.getLoanId()).stream()
                .filter(plan -> plan.getPeriod() >= repayTrailVo.getPeriod()).toList();
        return RepayConvert.INSTANCE.loanReplansToTrailResultVo(repayPlans);
    }

    /**
     * 代偿后还款试算 按日计息
     *
     * @param repayTrailVo  试算入参
     * @param guaranteeRate 融担费率 （为空时，融担费为零）
     * @param penaltyRate   罚息费率 （为空时，取代偿时的资方罚息）
     * @param yearDays      年计息天数（一年多少天）
     * @return
     */
    protected TrailResultVo claimAfterTrailByDate(RepayTrailVo repayTrailVo, BigDecimal guaranteeRate, BigDecimal penaltyRate, BigDecimal yearDays) {
        Loan loan = finRepayService.getLoan(repayTrailVo.getLoanId(), repayTrailVo.getOuterLoanId());
        // 当前期次还款计划
        LoanReplan currentRepayPlan = finRepayService.getRepayPlan(repayTrailVo.getLoanId(), repayTrailVo.getPeriod());
        BankLoanReplan bankRepayPlan = finRepayService.findBankRepayPlan(currentRepayPlan.getLoanId(), currentRepayPlan.getPeriod());

        // 代偿后还当期
        if (RepayPurpose.CURRENT.equals(repayTrailVo.getRepayPurpose())) {
            TrailResultVo trailResultVo = RepayConvert.INSTANCE.loanReplanToTrailResultVo(currentRepayPlan);
            trailResultVo.setPayee(Payee.GUARANTEE);
            return trailResultVo;
        }

        // 代偿后结清还款
        List<LoanReplan> repayPlans = finRepayService.getRepayPlans(loan.getId());
        TrailResultVo trailResultVo = new TrailResultVo();
        BigDecimal unpaidPrincipal = repayPlans.stream().filter(rp -> rp.getPeriod() >= repayTrailVo.getPeriod())
                .map(LoanReplan::getPrincipalAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
        trailResultVo.setPrincipal(unpaidPrincipal);
        trailResultVo.setInterest(dailyInterestCalculation(repayTrailVo.getPeriod(), loan, repayPlans, loan.getBankRate(), yearDays));
        // 罚息
        if (null == penaltyRate) {
            trailResultVo.setOverdueFee(bankRepayPlan.getActPenaltyAmt());
        } else {
            trailResultVo.setOverdueFee(dailyInterestCalculation(repayTrailVo.getPeriod(), loan, repayPlans, penaltyRate, yearDays));
        }
        // 融担费
        if (null == guaranteeRate) {
            trailResultVo.setGuaranteeFee(BigDecimal.ZERO);
        } else {
            trailResultVo.setGuaranteeFee(dailyInterestCalculation(repayTrailVo.getPeriod(), loan, repayPlans, guaranteeRate, yearDays));
        }
        trailResultVo.setBreachFee(bankRepayPlan.getActBreachAmt());
        trailResultVo.setPayee(Payee.GUARANTEE);
        trailResultVo.setAmount(AmountUtil.sum(trailResultVo.getPrincipal(), trailResultVo.getInterest(),
                trailResultVo.getGuaranteeFee(), trailResultVo.getOverdueFee(), trailResultVo.getBreachFee()));
        return trailResultVo;
    }

    /**
     * 提前结清试算校验
     */
    protected void clearTrailCheck(Loan loan, Integer period) {
        LoanReplan repayPlan = finRepayService.getRepayPlan(loan.getId(), period);
        if (LocalDate.now().isAfter(repayPlan.getRepayDate())) {
            throw new BizException(BizErrorCode.REPAY_TRIAL_ERROR.getCode(), "逾期不支持提前结清");
        }
        if (LocalDate.now().isEqual(repayPlan.getRepayDate())) {
            throw new BizException(BizErrorCode.REPAY_TRIAL_ERROR.getCode(), "账单日当天不支持提前结清");
        }
    }

    @Override
    public void bankRepayNotify(CustomerRepayRecord customerRepayRecord) {
        // 各资方自行实现
        throw new BizException(BizErrorCode.METHOD_NOT_IMPLEMENTS);
    }

    @Override
    public void bankSubstituteApply(BankBatchSubstituteRecord record) {
        logger.info("bank substitute apply, loanId: {}, period: {}, channel: {}", record.getLoanId(), record.getPeriod(), record.getChannel());
        String loanId = record.getLoanId();
        Integer period = record.getPeriod();

        // 查询需要代偿后还款通知资方的记录
        List<LoanReplan> needClaimAfterNotify = claimAfterNotifyUnSuccessRecord(loanId, record.getChannel().name(), period);

        if (CollectionUtil.isNotEmpty(needClaimAfterNotify)) {
            for (LoanReplan plan : needClaimAfterNotify) {
                // 创建代偿后还款通知记录并提交代偿后还款通知队列
                createClaimAfterNotifyRecordAndSubmit(plan);
            }
        }

        // 检查小于代还期数的代偿期数是否都通知资方成功
        if (!checkClaimAfterNotifySuccessRecord(loanId, period)) {
            getMqService().submitSubstituteBankApplyDelay(record.getId());
            return;
        }

        RepayTrailVo repayTrailVo = new RepayTrailVo();
        repayTrailVo.setRepayPurpose(RepayPurpose.CURRENT);
        repayTrailVo.setLoanId(loanId);
        repayTrailVo.setPeriod(period);

        TrailResultVo trailResult;
        try {
            trailResult = trail(repayTrailVo);
        } catch (Exception e) {
            logger.error("substitute trail error for loanId: {}, period: {}", loanId, period, e);
            getWarningService().warn("trail error for substituteId: " + record.getId());
            return;
        }

        updatePlan(loanId, period, trailResult);

        Optional<BankRepayRecord> repayRecord = getFinRepayService().findBySysIdAndRepayStatusIn(record.getId());
        if (repayRecord.isPresent()) {
            logger.error("substitute apply, bank repay record exists for loanId: {}, period: {}", loanId, period);
            getWarningService().warn("substitute apply bank repay record exists for substituteId: " + record.getId());
            return;
        }

        BankRepayRecord bankRepayRecord = processBankRepayRecord(record, trailResult, loanId, period);

        processSubstitutePushStatus(bankRepayRecord, PushStatus.PROCESSING);

        // 还款通知
        BigDecimal reduceGuarantee = BigDecimal.ZERO;

        RepayApplyVo applyVo = new RepayApplyVo();
        initRepayApplyVo(applyVo, loanId, period);

        RepayResultVo bankRepayResult = bankRepayApply(applyVo, bankRepayRecord, reduceGuarantee, record.getId());

        processSubstitutePushStatus(bankRepayRecord, PushStatus.FINISHED);

        if (bankRepayResult.getStatus() == ProcessStatus.FAIL) {
            getWarningService().warn("请求资方代还失败 loanId: " + loanId + ", period: " + period + ", channel: " + record.getChannel());
        }
    }

    /**
     * 更新还款计划
     */
    public void updatePlan(String loanId, Integer period, TrailResultVo trailResult) {
        // 更新还款计划
    }

    private void createClaimAfterNotifyRecordAndSubmit(LoanReplan plan) {
        Loan loan = commonService.findLoanById(plan.getLoanId());

        LoanReplan originPlan = getFinRepayService().getRepayPlan(plan.getLoanId(), plan.getPeriod());
        RepayApplyVo applyVo = new RepayApplyVo();
        applyVo.setLoanId(plan.getLoanId());
        applyVo.setPeriod(plan.getPeriod());
        applyVo.setRepayPurpose(RepayPurpose.CURRENT);
        applyVo.setRepayMode(RepayMode.OFFLINE);
        applyVo.setRepayType(RepayType.SUBSTITUTE);
        applyVo.setPrincipal(originPlan.getPrincipalAmt());
        applyVo.setGuaranteeFee(originPlan.getGuaranteeAmt());
        applyVo.setInterest(originPlan.getInterestAmt());
        BigDecimal total = AmountUtil.sum(applyVo.getPrincipal(), applyVo.getInterest(), applyVo.getGuaranteeFee());
        applyVo.setAmount(total);
        ClaimAfterRepayRecord claimAfterRecord = createClaimAfterRecord(originPlan, applyVo, loan);
        getMqService().submitClaimAfterNotify(claimAfterRecord.getId());
    }

    /**
     * 查询需要代偿后还款通知资方的记录
     */
    private List<LoanReplan> claimAfterNotifyUnSuccessRecord(String loanId, String bankChannel, Integer period) {
        // 对资已还，对客未还的还款计划
        List<LoanReplan> customNotClaimNotifyPlans = findBeforeSubstitutedCustomNotPaid(loanId, bankChannel, period);

        // 查出代偿后未还款通知资方的计划
        List<LoanReplan> unClaimNotifyPlan = customNotClaimNotifyPlans.stream()
                .filter(plan -> !finRepayService.isExistValidClaimNotifyRecord(plan.getLoanId(), plan.getPeriod())).toList();

        // 对未代偿通知的按期数从小到大排序
        return unClaimNotifyPlan.stream().sorted(Comparator.comparing(LoanReplan::getPeriod)).toList();
    }

    /**
     * 检查小于当前期数的代偿期数是否都通知资方成功
     */
    public boolean checkClaimAfterNotifySuccessRecord(String loanId, Integer period) {
        // 默认不需要代偿通知资方，需要的自行实现
        return true;
    }

    /**
     * 查询代还前，对客未还还款计划
     *
     * @return
     */
    public List<LoanReplan> findBeforeSubstitutedCustomNotPaid(String loanId, String channel, Integer period) {
        return new ArrayList<>();
    }

    /**
     * 创建代偿后还款记录
     *
     * @return
     */
    public ClaimAfterRepayRecord createClaimAfterRecord(LoanReplan originPlan, RepayApplyVo repayApplyDto, Loan loan) {
        return null;
    }

    /**
     * 代偿后还款
     *
     * @param recordId claimAfterRepayRecord 表id
     * @return
     */
    public RepayResultVo repayAfterClaimRemote(String recordId) {
        return new RepayResultVo();
    }

    public void initRepayApplyVo(RepayApplyVo applyVo, String loanId, Integer period) {
        applyVo.setLoanId(loanId);
        applyVo.setPeriod(period);
        applyVo.setRepayPurpose(RepayPurpose.CURRENT);
        applyVo.setRepayMode(RepayMode.OFFLINE);
        applyVo.setRepayType(RepayType.SUBSTITUTE);

        populateRepayApplyVo(applyVo, loanId);
    }

    /**
     * 创建对资记录
     * 金额：取当期账单【应还本金+应还利息+罚息（如有）】
     *
     * @return
     */
    public BankRepayRecord processBankRepayRecord(BankBatchSubstituteRecord record, TrailResultVo trailResult, String loanId, int period) {
        BankRepayRecord bankRepayRecord = initBankRepayRecord(trailResult, loanId, period);
        bankRepayRecord.setProjectCode(getFinRepayService().getLoan(loanId, null).getProjectCode()); //项目唯一编码
        bankRepayRecord.setTotalAmt(calculateTotalAmount(bankRepayRecord));

        bankRepayRecord.setLoanId(loanId);
        bankRepayRecord.setPeriod(period);
        bankRepayRecord.setRepayPurpose(RepayPurpose.CURRENT);
        bankRepayRecord.setRepayMode(RepayMode.OFFLINE);
        bankRepayRecord.setRepayType(RepayType.SUBSTITUTE);

        bankRepayRecord.setSysId(record.getId());
        bankRepayRecord.setChannel(record.getChannel());
        bankRepayRecord.setRepayStatus(ProcessStatus.INIT);
        bankRepayRecord.setRepayTime(LocalDateTime.now());
        return populateBankRepayRecord(trailResult, bankRepayRecord);
    }

    public BankRepayRecord initBankRepayRecord(TrailResultVo trailResult, String loanId, Integer period) {
        return new BankRepayRecord();
    }

    @Override
    public BankRepayRecord populateBankRepayRecord(TrailResultVo trailResult, BankRepayRecord record) {
        // 各资方自行按需求实现
        return getFinRepayService().saveBankRepayRecord(record);
    }

    public void populateRepayApplyVo(RepayApplyVo applyVo, String loanId) {
        // 各资方自行按需求实现
    }

    private BigDecimal calculateTotalAmount(BankRepayRecord bankRepayRecord) {
        return AmountUtil.sum(bankRepayRecord.getPrincipalAmt(), bankRepayRecord.getInterestAmt(), bankRepayRecord.getGuaranteeAmt(),
                bankRepayRecord.getPenaltyAmt(), bankRepayRecord.getBreachAmt());
    }

    @Override
    public void repayCustomRetry(CustomerRepayRecord customerRepayRecord) {
        if (customerRepayRecord.getRepayStatus().isFinal()) {
            // 还款已终态，无需重试还款
            return;
        }

        Optional<BankRepayRecord> bankRepayRecord = finRepayService.findBankRecordBySysId(customerRepayRecord.getId());
        if (bankRepayRecord.isPresent()) {
            // 存在对资记录，不可重试还款
            return;
        }

        if (getFinRepayService().existClaimedRepayPlan(customerRepayRecord.getLoanId(), customerRepayRecord.getPeriod())) {
            // 存在对资代偿记录，不可重试还款
            return;
        }

        LoanReplan repayPlan = finRepayService.getRepayPlan(customerRepayRecord.getLoanId(), customerRepayRecord.getPeriod());
        RepayMode repayMode = customerRepayRecord.getRepayMode();
        RepayResultVo repayResultVo;
        if (repayMode == RepayMode.ONLINE) {

            repayResultVo = onlineRepay(customerRepayRecord, repayPlan, null);
            // 对客还款记录
            customerRepayRecord.setRepayStatus(repayResultVo.getStatus());
            customerRepayRecord.setRemark(repayResultVo.getFailMsg());
            commonService.saveRepayRecord(customerRepayRecord);
        }
    }

    /**
     * 按日计息
     *
     * @param currentPeriod 当期期数（如：从第六期开始结清，传6）
     * @param loan          借据信息
     * @param repayPlans    还款计划信息
     * @param yearRate      计息年费率
     * @param yearDays      年计息天数
     * @return
     */
    protected BigDecimal dailyInterestCalculation(int currentPeriod, Loan loan, List<LoanReplan> repayPlans, BigDecimal yearRate, BigDecimal yearDays) {
        Map<Integer, LoanReplan> periodPlanMap = repayPlans.stream().collect(Collectors.toMap(LoanReplan::getPeriod, repayPlan -> repayPlan));
        LocalDate nowLocalDate = getLocalDateTime().toLocalDate();

        // 剩余未还本金
        BigDecimal unpaidPrincipal;
        // 计息天数
        BigDecimal days;

        // 是否首期
        if (currentPeriod == 1) {
            unpaidPrincipal = loan.getLoanAmt();
            days = new BigDecimal(ChronoUnit.DAYS.between(loan.getLoanTime().toLocalDate(), nowLocalDate));
        } else {
            unpaidPrincipal = repayPlans.stream().filter(rp -> rp.getPeriod() >= currentPeriod).map(LoanReplan::getPrincipalAmt)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            days = new BigDecimal(ChronoUnit.DAYS.between(periodPlanMap.get(currentPeriod - 1).getRepayDate(), nowLocalDate));
        }

        // 按日计息金额 = 剩余未还本金 * 计息天数 * 费率 / 年计息天数
        return unpaidPrincipal.multiply(days).multiply(yearRate).divide(yearDays, 2, RoundingMode.HALF_UP);
    }

    /**
     * 各资方自行实现
     *
     * @param customerRepayRecord
     * @param repayPlan
     * @return
     */
    public abstract RepayResultVo onlineRepay(CustomerRepayRecord customerRepayRecord, LoanReplan repayPlan, RepayApplyVo repayApplyDto);

    public abstract RepayResultVo offlineRepay(CustomerRepayRecord customerRepayRecord, LoanReplan loanReplan, RepayApplyVo repayApplyDto);

    public abstract RepayResultVo bankRepayApply(RepayApplyVo repayApplyDto, BankRepayRecord bankRepayRecord,
                                                 BigDecimal reduceGuaranteeAmt, String custRepayRecordId);

    @Override
    public PlanVo queryRepayPlan(PlanQueryVo planQueryVo) {
        // 查询本地还款计划
        String loanId = planQueryVo.getLoanId();
        Loan loan;
        if (!StringUtil.isEmpty(loanId)) {
            loan = getCommonService().findLoanById(loanId);
        } else {
            loan = getCommonService().findLoanByOutId(planQueryVo.getSysLoanId());
        }
        loanId = loan.getId();
        PlanVo planVo = new PlanVo();
        planVo.setLoanId(loanId);
        planVo.setLoanAmt(loan.getLoanAmt());
        planVo.setPeriods(loan.getPeriods());
        List<LoanReplan> repayPlans = getFinRepayService().getRepayPlans(loanId);
        List<PlanItemVo> planItemVos = new ArrayList<>();
        for (LoanReplan repayPlan : repayPlans) {
            PlanItemVo planItemVo = new PlanItemVo();
            //总金额
            planItemVo.setTotalAmt(repayPlan.getTotalAmt());
            //罚息
            planItemVo.setPenaltyAmt(repayPlan.getPenaltyAmt());
            planItemVo.setLoanId(repayPlan.getLoanId());
            planItemVo.setPeriod(repayPlan.getPeriod());
            planItemVo.setRepayDate(repayPlan.getRepayDate());
            // 资方本金
            planItemVo.setPrincipalAmt(repayPlan.getPrincipalAmt());
            // 资方利息
            planItemVo.setInterestAmt(repayPlan.getInterestAmt());
            // 融担费
            planItemVo.setGuaranteeAmt(repayPlan.getGuaranteeAmt());
            // 违约金
            planItemVo.setActBreachAmt(repayPlan.getBreachAmt());

            planItemVos.add(planItemVo);
        }
        planVo.setPlanItems(planItemVos);
        return planVo;
    }

    @Override
    public OverduePlanVo queryOverduePlan(OverduePlanQueryVo planQueryVo) {
        // 查询本地还款计划
        String loanId = planQueryVo.getLoanId();
        Loan loan;
        if (!StringUtil.isEmpty(loanId)) {
            loan = getCommonService().findLoanById(loanId);
        } else {
            loan = getCommonService().findLoanByOutId(planQueryVo.getSysLoanId());
        }
        loanId = loan.getId();
        OverduePlanVo planVo = new OverduePlanVo();
        planVo.setLoanId(loanId);
        planVo.setLoanAmt(loan.getLoanAmt());
        planVo.setPeriods(loan.getPeriods());
        List<LoanReplan> repayPlans = getFinRepayService().getRepayPlans(loanId);
        //List<PlanItemVo> planItems = QinjiaRepayConvert.INSTANCE.toPlanItems(repayPlans);
        //planVo.setPlanItems(planItems);
        return planVo;
    }

    @Override
    public void query(String repayId) {
        BankRepayRecord bankRepayRecord = finRepayService.getBankRepayRecord(repayId);
        if (bankRepayRecord.getRepayStatus().isFinal()) {
            logger.info("资方还款记录已终态，无需查询，bankRepayRecordId: {}", bankRepayRecord.getId());
            return;
        }

        RepayResultVo repayResultVo = bankQuery(repayId);
        // 还款结果
        if (ProcessStatus.PROCESSING.equals(repayResultVo.getStatus())) {
            mqService.submitRepayQueryDelay(bankRepayRecord.getId());
            return;
        }

        // 代还处理
        if (RepayType.SUBSTITUTE.equals(bankRepayRecord.getRepayType())) {
            processSubstitute(bankRepayRecord, repayResultVo);
            return;
        }

        CustomerRepayRecord customerRepayRecord = commonService.findRepayRecordById(bankRepayRecord.getSysId());
        if (ProcessStatus.FAIL.equals(repayResultVo.getStatus())) {
            // 对资
            bankRepayRecord.setRepayStatus(ProcessStatus.FAIL);
            bankRepayRecord.setRemark(repayResultVo.getFailMsg());
            finRepayService.updateBankRepayRecord(bankRepayRecord);
            // 对客
            if (RepayMode.ONLINE.equals(customerRepayRecord.getRepayMode())) {
                customerRepayRecord.setRepayStatus(ProcessStatus.FAIL);
                customerRepayRecord.setRemark(repayResultVo.getFailMsg());
                finRepayService.updateCustomRepayRecord(customerRepayRecord);
            }
            return;
        }

        if (ProcessStatus.SUCCESS.equals(repayResultVo.getStatus())) {
            // 还款成功时间
            bankRepayRecord.setRepayTime(repayResultVo.getActRepayTime());
            bankRepayRecord.setPayOrderNo(null == repayResultVo.getPayOrderNo() ? bankRepayRecord.getPayOrderNo() : repayResultVo.getPayOrderNo());
            // 更新资方还款流水号
            if (StringUtil.isBlank(bankRepayRecord.getBankSerial()) && StringUtil.isNotBlank(repayResultVo.getBankSeq())) {
                bankRepayRecord.setBankSerial(repayResultVo.getBankSeq());
            }
            customerRepayRecord.setRepayTime(repayResultVo.getActRepayTime());
            customerRepayRecord.setBankPenaltyAmt(bankRepayRecord.getPenaltyAmt());
            customerRepayRecord.setPayOrderNo(null == repayResultVo.getPayOrderNo() ? customerRepayRecord.getPayOrderNo() : repayResultVo.getPayOrderNo());
            doRepaySuccess(bankRepayRecord, customerRepayRecord);

            // 判断结清
            RepayPurpose repayPurpose = bankRepayRecord.getRepayPurpose();
            if (repayPurpose != RepayPurpose.CURRENT) {
                modifyClearRepayStatus(bankRepayRecord.getLoanId(), bankRepayRecord.getPeriod());
            }
            // 更新在贷
            changeOnLoan(bankRepayRecord.getLoanId(), LoanStage.AFTER_LOAN, bankRepayRecord.getPrincipalAmt());
        }
    }

    protected void processSubstitute(BankRepayRecord bankRepayRecord, RepayResultVo repayResultVo) {
        if (ProcessStatus.FAIL.equals(repayResultVo.getStatus())) {
            // 对资
            bankRepayRecord.setRepayStatus(ProcessStatus.FAIL);
            bankRepayRecord.setRemark(repayResultVo.getFailMsg());
            finRepayService.updateBankRepayRecord(bankRepayRecord);

            BankBatchSubstituteRecord bankBatchSubstituteRecord = commonService.findBySubstituteId(bankRepayRecord.getSysId());
            bankBatchSubstituteRecord.setSubstituteStatus(ProcessStatus.PROCESSING);
            bankBatchSubstituteRecord.setRemark(repayResultVo.getFailMsg());
            commonService.saveBankSubstituteRecord(bankBatchSubstituteRecord);
            getWarningService().warn("代还失败, 请手动处理, loan_id:" + bankRepayRecord.getLoanId());
            return;
        }

        if (ProcessStatus.SUCCESS.equals(repayResultVo.getStatus())) {
            // 还款成功时间
            bankRepayRecord.setRepayTime(repayResultVo.getActRepayTime());
            bankRepayRecord.setPayOrderNo(repayResultVo.getPayOrderNo());
            // 更新资方还款流水号
            if (StringUtil.isBlank(bankRepayRecord.getBankSerial()) && StringUtil.isNotBlank(repayResultVo.getBankSeq())) {
                bankRepayRecord.setBankSerial(repayResultVo.getBankSeq());
            }

            doSubstituteSuccess(bankRepayRecord);
            // 更新在贷
            changeOnLoan(bankRepayRecord.getLoanId(), LoanStage.AFTER_LOAN, bankRepayRecord.getPrincipalAmt());
        }
    }

    /**
     * 更改在贷
     */
    public void changeOnLoan(String loanId, LoanStage stage, BigDecimal amount) {
        //
    }

    @Override
    public ClaimMarkResultVo claimMark(ClaimMarkApplyVo applyVo) {
        logger.info("代偿标记开始, 请求参数:{}", JsonUtil.toJsonString(applyVo));
        // 应还日
        LocalDate repayDay = applyVo.getRepayDay();
        // 还款计划
        List<LoanReplan> claimPlans = getFinRepayService().findRepayPlanBankNotPaid(repayDay, applyVo.getChannel());
        logger.info("需要代偿标记的数量:{}", claimPlans.size());


        List<BankLoanReplan> results = new ArrayList<>();
        for (LoanReplan lp : claimPlans) {
            var loanId = lp.getLoanId();
            List<BankLoanReplan> result;
            try {
                result = processClaimBankPlan(loanId, lp.getPeriod());
            } catch (Exception e) {
                logger.error("代偿标记异常", e);
                getWarningService().warn("代偿标记异常,bankChannel:" + applyVo.getChannel() + "loanReplanId:" + lp.getId());
                continue;
            }
            for (BankLoanReplan blp : result) {
                String loanId1 = blp.getLoanId();
                final Integer targetPeriod = blp.getPeriod();
                LoanReplan repayPlan = getFinRepayService().getRepayPlan(loanId1, targetPeriod);
                repayPlan.setBankRepayStatus(RepayStatus.REPAID);
                repayPlan.setPenaltyAmt(blp.getActPenaltyAmt());
                getFinRepayService().updateRepayPlan(repayPlan);
                // 处理代偿回购场景
                if (blp.getRepayPurpose() == RepayPurpose.CLEAR) {
                    getFinRepayService().getRepayPlans(loanId1).stream().filter(p -> p.getPeriod().compareTo(targetPeriod) > 0).forEach(p -> {
                        p.setBankRepayStatus(RepayStatus.REPAID);
                        p.setPenaltyAmt(blp.getActPenaltyAmt());
                        getFinRepayService().updateRepayPlan(p);
                    });
                }
                results.add(blp);
            }
        }
        // 生成给entrance的代偿标记文件
        //        try {
        ////            getInnerClaimMarkService().generate360ClaimMark(repayDay, results, BankChannel.YL_QJ);
        //        } catch (Exception ex) {
        //            getWarningService().warn("core生成给entrance的代偿文件出错");
        //        }
        // 资方代偿发起
        asyncClaimApply(results, applyVo);

        ClaimMarkResultVo resultVo = new ClaimMarkResultVo();
        resultVo.setStatus(ProcessStatus.SUCCESS);
        return resultVo;
    }

    /**
     * 资方异步发起代偿
     */
    public void asyncClaimApply(List<BankLoanReplan> results, ClaimMarkApplyVo applyVo) {

    }

    /**
     * 重推代偿申请
     *
     * @param claimRetryVo
     * @return
     */
    @Override
    public ClaimRetryResultVo claimRetry(ClaimRetryVo claimRetryVo) {
        LocalDate claimDay = claimRetryVo.getClaimDay();
        BankChannel bankChannel = claimRetryVo.getBankChannel();
        LocalDateTime startDateTime = claimDay.atStartOfDay();
        LocalDateTime endDateTime = claimDay.plusDays(1).atStartOfDay();
        List<BankLoanReplan> claims = finRepayService.getClaimBankRepayPlans(bankChannel, startDateTime, endDateTime);

        // 回购
        Map<String, List<BankLoanReplan>> clearListMap = claims.stream().filter(bankLoanReplan -> RepayPurpose.CLEAR.equals(bankLoanReplan.getRepayPurpose()))
                .collect(Collectors.groupingBy(BankLoanReplan::getLoanId));

        // 代偿
        List<BankLoanReplan> currentList = claims.stream().filter(bankLoanReplan -> RepayPurpose.CURRENT.equals(bankLoanReplan.getRepayPurpose())).toList();

        // 重推回购
        for (List<BankLoanReplan> value : clearListMap.values()) {
            BankLoanReplan bankLoanReplan = value.stream().reduce((b1, b2) -> b1.getPeriod() < b2.getPeriod() ? b1 : b2).orElse(null);
            if (null == bankLoanReplan) {
                continue;
            }

            if (checkRetryClaim(bankLoanReplan.getLoanId(), bankLoanReplan.getPeriod())) {
                getMqService().submitClaimApplyDelay(bankLoanReplan.getId());
            }
        }

        // 重推代偿
        for (BankLoanReplan bankLoanReplan : currentList) {
            if (checkRetryClaim(bankLoanReplan.getLoanId(), bankLoanReplan.getPeriod())) {
                getMqService().submitClaimApplyDelay(bankLoanReplan.getId());
            }
        }

        ClaimRetryResultVo resultVo = new ClaimRetryResultVo();
        resultVo.setStatus(ProcessStatus.SUCCESS);
        return resultVo;
    }

    /**
     * 还款通知
     *
     * @param repayNoticeVo
     * @return
     */
    @Override
    public RepayNoticeResultVo repayNotice(RepayNoticeVo repayNoticeVo) {
        return null;
    }

    /**
     * 主动发起代偿
     *
     * @param vo
     * @return
     */
    @Override
    public ActiveLaunchClaimResultVo activeLaunchClaim(ActiveLaunchClaimApplyVo vo) {
        List<LaunchClaimVo> launchClaims = vo.getLaunchClaims();
        if (CollectionUtil.isEmpty(launchClaims)) {
            return null;
        }

        for (LaunchClaimVo launchClaim : launchClaims) {
            if (StringUtil.isEmpty(launchClaim.getLoanId())) {
                continue;
            }

            if (null == launchClaim.getPeriod()) {
                getWarningService().warn("主动发起代偿，代偿期次不能为空，loanId:" + launchClaim.getLoanId());
                logger.info("主动发起代偿，代偿期次不能为空，loanId: {}", launchClaim.getLoanId());
                continue;
            }

            if (null == launchClaim.getRepayPurpose()) {
                getWarningService().warn("主动发起代偿，还款模式不能为空，loanId:" + launchClaim.getLoanId());
                logger.info("主动发起代偿，还款模式不能为空，loanId: {}", launchClaim.getLoanId());
                continue;
            }

            // 查询当期还款计划
            LoanReplan loanReplan = finRepayService.getRepayPlan(launchClaim.getLoanId(), launchClaim.getPeriod());
            if (RepayStatus.REPAID.equals(loanReplan.getBankRepayStatus())) {
                getWarningService().warn("主动发起代偿，当前借据期次对资已偿还，loanId:" + loanReplan.getLoanId() + ", period:" + loanReplan.getPeriod());
                logger.info("主动发起代偿，当前借据期次对资已偿还，loanId: {}, period: {}", loanReplan.getLoanId(), loanReplan.getPeriod());
                continue;
            }

            // 不允许提前还当期
            if (RepayPurpose.CURRENT.equals(launchClaim.getRepayPurpose()) && loanReplan.getRepayDate().isAfter(getLocalDateTime().toLocalDate())) {
                getWarningService().warn("主动发起代偿，不允许提前还当期，loanId:" + loanReplan.getLoanId() + ", period:" + loanReplan.getPeriod());
                logger.info("主动发起代偿，不允许提前还当期，loanId: {}, period: {}", loanReplan.getLoanId(), loanReplan.getPeriod());
                continue;
            }

            // 不允许越期还款
            if (launchClaim.getPeriod() > 1) {
                List<BankRepayRecord> bankRepayRecords = finRepayService
                        .findBankRepayRecordsByLoanIdAndPeriod(launchClaim.getLoanId(), launchClaim.getPeriod() - 1);

                // 是否存在比当前期数小一期，且还款成功的数据，true 存在，false 不存在
                boolean check = bankRepayRecords.stream().anyMatch(bankRepayRecord -> ProcessStatus.SUCCESS.equals(bankRepayRecord.getRepayStatus()));

                if (!check) {
                    getWarningService().warn("主动发起代偿，存在小于当前期次的未还数据，loanId:" + loanReplan.getLoanId() + ", period:" + loanReplan.getPeriod());
                    logger.info("主动发起代偿，存在小于当前期次的未还数据，loanId: {}, period: {}", loanReplan.getLoanId(), loanReplan.getPeriod());
                    continue;
                }
            }

            // 校验正在进行中的还款
            List<CustomerRepayRecord> customerRepayRecords = finRepayService.getCustomRepayRecordsByLoanIdAndPeriod(launchClaim.getLoanId(),
                    launchClaim.getPeriod());
            List<BankRepayRecord> bankRepayRecords = finRepayService.findBankRepayRecordsByLoanIdAndPeriod(launchClaim.getLoanId(), launchClaim.getPeriod());
            if (CollectionUtil.isNotEmpty(customerRepayRecords) || CollectionUtil.isNotEmpty(bankRepayRecords)) {

                // 是否存在非终态的对客还款记录，true 存在，false 不存在
                boolean checkCustomerRepayRecords = customerRepayRecords.stream().anyMatch(bankRepayRecord -> !bankRepayRecord.getRepayStatus().isFinal());

                // 是否存在非终态的对资还款记录，true 存在，false 不存在
                boolean checkBankRepayRecords = bankRepayRecords.stream().anyMatch(bankRepayRecord -> !bankRepayRecord.getRepayStatus().isFinal());

                if (checkCustomerRepayRecords || checkBankRepayRecords) {
                    getWarningService().warn("主动发起代偿，存在正在进行中的还款，loanId:" + loanReplan.getLoanId() + ", period:" + loanReplan.getPeriod());
                    logger.info("主动发起代偿，存在正在进行中的还款，loanId: {}, period: {}", loanReplan.getLoanId(), loanReplan.getPeriod());
                    continue;
                }
            }

            // 生成对资代偿实还计划
            BankLoanReplan currentBankLoanReplan = RepayConvert.INSTANCE.initClaimBankLoanReplan(loanReplan, launchClaim.getRepayPurpose());
            currentBankLoanReplan = finRepayService.saveBankRepayPlan(currentBankLoanReplan);
            // 更新对资状态未已还
            loanReplan.setBankRepayStatus(RepayStatus.REPAID);
            loanReplan.setPenaltyAmt(currentBankLoanReplan.getActPenaltyAmt());
            finRepayService.updateRepayPlan(loanReplan);

            // 回购（代偿结清）
            if (RepayPurpose.CLEAR.equals(launchClaim.getRepayPurpose())) {
                List<LoanReplan> repayPlans = finRepayService.getRepayPlans(launchClaim.getLoanId());
                repayPlans.stream().filter(repayPlan -> repayPlan.getPeriod() > loanReplan.getPeriod()).forEach(repayPlan -> {
                    // 生成对资代偿实还计划
                    BankLoanReplan bankLoanReplan = RepayConvert.INSTANCE.initClaimBankLoanReplan(repayPlan, RepayPurpose.CLEAR);
                    finRepayService.saveBankRepayPlan(bankLoanReplan);
                    // 更新对资状态未已还
                    repayPlan.setPenaltyAmt(bankLoanReplan.getActPenaltyAmt());
                    repayPlan.setBankRepayStatus(RepayStatus.REPAID);
                    finRepayService.updateRepayPlan(repayPlan);
                });
            }

            // mq主动代偿申请
            getMqService().submitClaimApplyDelay(currentBankLoanReplan.getId());
        }

        return null;
    }

    /**
     * 获取时间
     *
     * @return
     */
    protected LocalDateTime getLocalDateTime() {
        return LocalDateTime.now();
    }

    /**
     * 校验是否需要重推代偿申请
     * （不存在已成功或者处理中的还款记录，即为需要重推）
     *
     * @param loanId
     * @param period
     * @return
     */
    private boolean checkRetryClaim(String loanId, Integer period) {
        List<BankRepayRecord> bankRepayRecords = finRepayService.findBankRepayRecordsByLoanIdAndPeriod(loanId, period);

        if (null == bankRepayRecords || bankRepayRecords.isEmpty()) {
            return true;
        }

        return bankRepayRecords.stream().noneMatch(bankRepayRecord ->
                ProcessStatus.PROCESSING.equals(bankRepayRecord.getRepayStatus()) || ProcessStatus.SUCCESS.equals(bankRepayRecord.getRepayStatus()));
    }

    @Override
    public ActiveClaimApplyResultVo activeClaimApply(ActiveClaimApplyVo vo) {
        // 主动代偿申请，默认为空方法
        return null;
    }

    @Override
    public ActiveClaimQueryResultVo activeClaimQuery(ActiveClaimQueryVo vo) {
        // 主动代偿查询，默认为空方法
        return null;
    }

    @Override
    public void claimApply(String bankLoanReplanId) {
        // 代偿申请，默认为空方法
    }

    @Override
    public void claimQuery(String bankRepayRecordId) {
        // 代偿申请查询，默认为空方法
    }

    @Override
    public void flowClaimApply(FlowClaimRecord flowClaimRecord) {
        // 代偿申请，默认为空方法
    }

    @Override
    public void flowClaimQuery(FlowClaimRecord flowClaimRecord) {
        // 代偿申请查询，默认为空方法
    }

    @Override
    public void claimAfterNotify(String recordId) {
        // 检查小于当前期数的代偿后通知资方是否都成功
        if (checkAllClaimAfterNotifySuccess(recordId)) {
            repayAfterClaimRemote(recordId);
            return;
        }
        getMqService().submitClaimAfterNotifyDelay(recordId);
    }

    /**
     * 检查小于当前期数的代偿期数是否都通知资方成功
     */
    protected boolean checkAllClaimAfterNotifySuccess(String recordId) {
        ClaimAfterRepayRecord claimAfterRecord = getFinRepayService().findClaimAfterRecordById(recordId);
        if (Objects.isNull(claimAfterRecord)) {
            return false;
        }

        return checkClaimAfterNotifySuccessRecord(claimAfterRecord.getLoanId(), claimAfterRecord.getPeriod());
    }

    protected void doRepaySuccess(BankRepayRecord bankRepayRecord, CustomerRepayRecord customerRepayRecord) {
        // 还款计划
        String loanId = bankRepayRecord.getLoanId();
        Integer period = bankRepayRecord.getPeriod();
        LoanReplan loanReplan = finRepayService.getRepayPlan(loanId, period);
        loanReplan.setBankRepayStatus(RepayStatus.REPAID);
        loanReplan.setPenaltyAmt(bankRepayRecord.getPenaltyAmt());
        // 非代还，才需要更新对客还款状态
        if (!RepayType.SUBSTITUTE.equals(bankRepayRecord.getRepayType())) {
            loanReplan.setCustRepayStatus(RepayStatus.REPAID);
        }
        finRepayService.updateRepayPlan(loanReplan);

        // 对资还款计划
        BankLoanReplan bankLoanReplan = getFinRepayService().findBankRepayPlan(loanId, period);
        if (Objects.isNull(bankLoanReplan)) {
            bankLoanReplan = RepayConvert.INSTANCE.toPlanEntity(bankRepayRecord);
            bankLoanReplan.setId(loanReplan.getId());
        }
        bankLoanReplan.setRepayStatus(RepayStatus.REPAID);
        finRepayService.saveBankRepayPlan(bankLoanReplan);

        // 对资还款记录
        bankRepayRecord.setRepayStatus(ProcessStatus.SUCCESS);
        if (RepayMode.ONLINE.equals(customerRepayRecord.getRepayMode())) {
            // 对客还款计划
            CustomerLoanReplan customerLoanReplan = getFinRepayService().getCustomRepayPlan(loanId, period);
            if (Objects.isNull(customerLoanReplan)) {
                customerLoanReplan = RepayConvert.INSTANCE.toPlanEntity(customerRepayRecord);
                customerLoanReplan.setId(loanReplan.getId());
            }

            customerLoanReplan.setRepayStatus(RepayStatus.REPAID);
            finRepayService.saveCustomerRepayPlan(customerLoanReplan);

            // 对客还款记录
            customerRepayRecord.setRepayStatus(ProcessStatus.SUCCESS);

            String payOrderNo = customerRepayRecord.getPayOrderNo();
            if (StringUtil.isEmpty(payOrderNo)) {
                customerRepayRecord.setPayOrderNo(bankRepayRecord.getBankBizId());
            }

            String bankSerial = customerRepayRecord.getBankSerial();
            if (StringUtil.isEmpty(bankSerial)) {
                customerRepayRecord.setBankSerial(bankRepayRecord.getBankSerial());
            }
            finRepayService.updateCustomRepayRecord(customerRepayRecord);
        } else if (RepayMode.OFFLINE.equals(customerRepayRecord.getRepayMode())) {
            CustomerRepayRecord repayRecord = getCommonService().findRepayRecordById(bankRepayRecord.getSysId());
            //线下还款以客户发起还款时间为为准
            logger.info("线下还款以客户发起还款时间为为准：对客时间：{}", repayRecord.getRepayTime());
            bankRepayRecord.setRepayTime(repayRecord.getRepayTime());
        }
        finRepayService.updateBankRepayRecord(bankRepayRecord);
        logger.info("线下还款以客户发起还款时间为为准：对资时间：{}", bankRepayRecord.getRepayTime());
    }

    protected void doSubstituteSuccess(BankRepayRecord bankRepayRecord) {
        // 还款计划
        String loanId = bankRepayRecord.getLoanId();
        Integer period = bankRepayRecord.getPeriod();
        LoanReplan loanReplan = finRepayService.getRepayPlan(loanId, period);
        loanReplan.setBankRepayStatus(RepayStatus.REPAID);
        // 还款计划表 添加资方罚息
        loanReplan.setPenaltyAmt(bankRepayRecord.getPenaltyAmt());
        finRepayService.updateRepayPlan(loanReplan);

        // 对资还款计划
        BankLoanReplan bankLoanReplan = getFinRepayService().findBankRepayPlan(loanId, period);
        if (Objects.isNull(bankLoanReplan)) {
            bankLoanReplan = RepayConvert.INSTANCE.toPlanEntity(bankRepayRecord);
            bankLoanReplan.setId(loanReplan.getId());
        }
        bankLoanReplan.setRepayStatus(RepayStatus.REPAID);
        finRepayService.saveBankRepayPlan(bankLoanReplan);

        // 对资还款记录
        bankRepayRecord.setRepayStatus(ProcessStatus.SUCCESS);
        bankRepayRecord = finRepayService.updateBankRepayRecord(bankRepayRecord);

        doBankSubstituteRecordSuccess(bankRepayRecord);
    }

    /**
     * 新增对客还款计划，并更新还款计划为已还，包括结清
     *
     * @param customerRecordId
     */
    protected void doCustomLoanPlanSuccess(String customerRecordId) {
        CustomerRepayRecord customRecord = getCommonService().findRepayRecordById(customerRecordId);

        // 还款计划
        LoanReplan loanReplan = getFinRepayService().getRepayPlan(customRecord.getLoanId(), customRecord.getPeriod());
        loanReplan.setCustRepayStatus(RepayStatus.REPAID);
        // 还款计划添加资方罚息
        loanReplan.setPenaltyAmt(customRecord.getPenaltyAmt());
        getFinRepayService().updateRepayPlan(loanReplan);

        // 对客还款计划
        CustomerLoanReplan customerLoanReplan = RepayConvert.INSTANCE.toPlanEntity(customRecord);
        customerLoanReplan.setId(loanReplan.getId());
        customerLoanReplan.setRepayStatus(RepayStatus.REPAID);
        getFinRepayService().saveCustomerRepayPlan(customerLoanReplan);

        // 结清
        if (!RepayPurpose.CURRENT.equals(customRecord.getRepayPurpose())) {
            List<LoanReplan> repayPlans = getFinRepayService().getRepayPlans(customRecord.getLoanId());
            repayPlans.stream().filter(p -> p.getPeriod().compareTo(customRecord.getPeriod()) > 0).forEach(p -> {
                var custRepayStatus = p.getCustRepayStatus();
                if (custRepayStatus == RepayStatus.NORMAL) {
                    p.setCustRepayStatus(RepayStatus.REPAID);
                    getFinRepayService().updateRepayPlan(p);
                }
            });
        }
    }

    protected void doCustomReplanSuccess(String recordId) {
        CustomerRepayRecord customRecord = getCommonService().findRepayRecordById(recordId);
        // 还款计划
        Integer period = customRecord.getPeriod();
        LoanReplan loanReplan;
        if (period == null) { // 结清
            List<LoanReplan> repayPlans = getFinRepayService().getRepayPlans(customRecord.getLoanId());
            loanReplan = repayPlans.stream().filter(p -> p.getCustRepayStatus() == RepayStatus.NORMAL)
                    .min(Comparator.comparing(LoanReplan::getPeriod)).orElseThrow(() -> new BizException(BizErrorCode.REPAY_INVALID));
        } else {
            loanReplan = getFinRepayService().getRepayPlan(customRecord.getLoanId(), period);
        }
        loanReplan.setCustRepayStatus(RepayStatus.REPAID);
        // 还款计划表 添加资方罚息
        loanReplan.setPenaltyAmt(customRecord.getPenaltyAmt());
        getFinRepayService().updateRepayPlan(loanReplan);

        // 对客还款计划
        CustomerLoanReplan customerLoanReplan = RepayConvert.INSTANCE.toPlanEntity(customRecord);
        customerLoanReplan.setId(loanReplan.getId());
        customerLoanReplan.setRepayStatus(RepayStatus.REPAID);
        getFinRepayService().saveCustomerRepayPlan(customerLoanReplan);
    }

    protected void modifyClearCustomAndBankStatus(String loanId, Integer period) {
        List<LoanReplan> repayPlans = getFinRepayService().getRepayPlans(loanId);
        repayPlans.stream().filter(p -> p.getPeriod().compareTo(period) > 0).forEach(p -> {
            if (p.getBankRepayStatus() == RepayStatus.NORMAL) {
                p.setBankRepayStatus(RepayStatus.REPAID);
            }
            if (p.getCustRepayStatus() == RepayStatus.NORMAL) {
                p.setCustRepayStatus(RepayStatus.REPAID);
            }
            getFinRepayService().updateRepayPlan(p);
        });
    }

    protected void modifyClearCustomStatus(String loanId, Integer period) {
        List<LoanReplan> repayPlans = getFinRepayService().getRepayPlans(loanId);
        repayPlans.stream().filter(p -> p.getPeriod().compareTo(period) > 0).forEach(p -> {
            var custRepayStatus = p.getCustRepayStatus();
            if (custRepayStatus == RepayStatus.NORMAL) {
                p.setCustRepayStatus(RepayStatus.REPAID);
                getFinRepayService().updateRepayPlan(p);
            }
        });
    }

    protected void modifyClearBankStatus(String loanId, Integer period) {
        List<LoanReplan> repayPlans = getFinRepayService().getRepayPlans(loanId);
        repayPlans.stream().filter(p -> p.getPeriod().compareTo(period) > 0).forEach(p -> {
            var bankRepayStatus = p.getBankRepayStatus();
            if (bankRepayStatus == RepayStatus.NORMAL) {
                p.setBankRepayStatus(RepayStatus.REPAID);
                getFinRepayService().updateRepayPlan(p);
            }
        });
    }

    /**
     * 代偿还款后更新对客还款计划
     *
     * @param loanId 借据id
     * @param period 期数
     */
    protected void doCustomReplanClaimSuccess(String loanId, Integer period, String custRepayRecordId) {
        CustomerRepayRecord customRecord = getCommonService().findRepayRecordById(custRepayRecordId);
        LoanReplan loanReplan = getFinRepayService().getRepayPlan(loanId, period);

        CustomerLoanReplan customerLoanReplan = RepayConvert.INSTANCE.toPlanEntity(customRecord);
        customerLoanReplan.setId(loanReplan.getId());
        customerLoanReplan.setRepayStatus(RepayStatus.REPAID);
        getFinRepayService().saveCustomerRepayPlan(customerLoanReplan);
    }

    /**
     * 修改结清时剩余期数的状态
     *
     * @param loanId 借据id
     * @param period 期数
     */
    protected void modifyClearRepayStatus(String loanId, Integer period) {
        List<LoanReplan> repayPlans = finRepayService.getRepayPlans(loanId);
        repayPlans.stream().filter(p -> p.getPeriod().compareTo(period) > 0).forEach(p -> {
            var bankRepayStatus = p.getBankRepayStatus();
            var custRepayStatus = p.getCustRepayStatus();
            if (bankRepayStatus == RepayStatus.NORMAL) {
                p.setBankRepayStatus(RepayStatus.REPAID);
            }
            if (custRepayStatus == RepayStatus.NORMAL) {
                p.setCustRepayStatus(RepayStatus.REPAID);
            }
            finRepayService.updateRepayPlan(p);
        });

    }

    protected void updateSuccess(BankRepayRecord bankRepayRecord) {
        LoanReplan originPlan = getFinRepayService().getRepayPlan(bankRepayRecord.getLoanId(), bankRepayRecord.getPeriod());
        if (bankRepayRecord.getRepayType() != RepayType.SUBSTITUTE) {
            originPlan.setCustRepayStatus(RepayStatus.REPAID);
        }
        originPlan.setBankRepayStatus(RepayStatus.REPAID);
        // 还款计划表添加资方罚息
        originPlan.setPenaltyAmt(bankRepayRecord.getPenaltyAmt());
        getFinRepayService().updateRepayPlan(originPlan);
        // 对资还款计划插入一条数据
        if (bankRepayRecord != null) {
            BankLoanReplan bankPlan = RepayConvert.INSTANCE.toPlanEntity(bankRepayRecord);
            bankPlan.setId(originPlan.getId());
            bankPlan.setRepayStatus(RepayStatus.REPAID);
            getFinRepayService().saveBankRepayPlan(bankPlan);
            if (bankRepayRecord.getRepayMode() == RepayMode.ONLINE) {
                // 更新对客还款计划
                if (bankRepayRecord.getRepayType() != RepayType.CLAIM && bankRepayRecord.getRepayType() != RepayType.SUBSTITUTE) {
                    String customRepayRecordId = bankRepayRecord.getSysId();
                    if (customRepayRecordId == null) {
                        getWarningService().warn("对资还款记录[" + bankRepayRecord.getId() + "]中对客还款记录Id不存在");
                    } else {
                        doCustomReplanSuccess(customRepayRecordId);
                    }
                }
            }
            // 修改结清时剩余期数
            if (bankRepayRecord.getRepayPurpose() != RepayPurpose.CURRENT) {
                modifyClearCustomAndBankStatus(bankRepayRecord.getLoanId(), bankRepayRecord.getPeriod());
            }
            bankRepayRecord.setRepayStatus(ProcessStatus.SUCCESS);
            getFinRepayService().updateBankRepayRecord(bankRepayRecord);

            if (bankRepayRecord.getRepayType() == RepayType.SUBSTITUTE) {
                doBankSubstituteRecordSuccess(bankRepayRecord);
            }
        }
    }

    protected void doBankSubstituteRecordSuccess(BankRepayRecord bankRepayRecord) {
        BankBatchSubstituteRecord bankBatchSubstituteRecord = commonService.findBySubstituteId(bankRepayRecord.getSysId());
        bankBatchSubstituteRecord.setActTotalAmt(bankRepayRecord.getTotalAmt());
        bankBatchSubstituteRecord.setActPrincipalAmt(bankRepayRecord.getPrincipalAmt());
        bankBatchSubstituteRecord.setActInterestAmt(bankRepayRecord.getInterestAmt());
        bankBatchSubstituteRecord.setActPenaltyAmt(bankRepayRecord.getPenaltyAmt());
        bankBatchSubstituteRecord.setActGuaranteeAmt(bankRepayRecord.getGuaranteeAmt());
        bankBatchSubstituteRecord.setActBreachAmt(bankRepayRecord.getBreachAmt());
        bankBatchSubstituteRecord.setActConsultAmt(bankRepayRecord.getConsultAmt());
        bankBatchSubstituteRecord.setActRepayTime(LocalDateTime.now());
        bankBatchSubstituteRecord.setSubstituteStatus(ProcessStatus.SUCCESS);
        bankBatchSubstituteRecord.setPushStatus(PushStatus.FINISHED);
        bankBatchSubstituteRecord.setActPushTime(LocalDateTime.now());
        bankBatchSubstituteRecord.setBankBizId(bankRepayRecord.getBankBizId());
        bankBatchSubstituteRecord.setBankSerial(bankRepayRecord.getBankSerial());
        commonService.saveBankSubstituteRecord(bankBatchSubstituteRecord);
    }

    protected void processSubstitutePushStatus(BankRepayRecord bankRepayRecord, PushStatus pushStatus) {
        BankBatchSubstituteRecord bankBatchSubstituteRecord = commonService.findBySubstituteId(bankRepayRecord.getSysId());
        bankBatchSubstituteRecord.setSubstituteStatus(ProcessStatus.PROCESSING);
        bankBatchSubstituteRecord.setPushStatus(pushStatus);
        bankBatchSubstituteRecord.setActPushTime(LocalDateTime.now());
        commonService.saveBankSubstituteRecord(bankBatchSubstituteRecord);
    }

    public abstract RepayResultVo bankQuery(String repayId);

    @Override
    public CompensatedRepaySyncRlt compensatedRepaySync(CompensatedRepaySyncVo syncVo) {
        // 代偿后还款同步记录
        CompensatedRepayNotify compensatedRepayNotify = finRepayService.saveCompensatedRepayNotify(RepayConvert.INSTANCE.toEntity(syncVo));
        // 明细
        syncVo.getRepaymentDetails().forEach(repaymentDetail -> {
            CompensatedRepayNotifyDetail detail = RepayConvert.INSTANCE.toEntity(repaymentDetail);
            detail.setRepayNotifyId(compensatedRepayNotify.getId());
            detail.setLoanId(compensatedRepayNotify.getLoanId());
            detail.setOuterLoanId(compensatedRepayNotify.getOuterLoanId());
            finRepayService.saveCompensatedRepayDetail(detail);
        });

        syncVo.setRepayNo(compensatedRepayNotify.getId());
        return bankCompensatedRepaySync(syncVo);
    }

    /**
     * 处理代偿的对资还款计划
     *
     * @param loanId 借据id
     * @param period 期数
     * @return
     */
    protected List<BankLoanReplan> processClaimBankPlan(String loanId, Integer period) {
        List<BankLoanReplan> bankRepayPlans = getFinRepayService().getBankRepayPlans(loanId);

        BankLoanReplan currentBankPlan = bankRepayPlans.stream().filter(p -> p.getPeriod().compareTo(period) == 0).findAny().orElse(null);

        if (currentBankPlan != null) {
            logger.error("对资已代偿, 对资还款计划已存在, loanId: {}, period: {}", loanId, period);
            getWarningService().warn("代偿对资还款计划已存在. loanId: " + loanId + ", period: " + period);
            return new ArrayList<>();
        }
        List<LoanReplan> repayPlans = getFinRepayService().getRepayPlans(loanId);
        Loan loan = getCommonService().findLoanById(loanId);
        List<BankLoanReplan> historyList = bankRepayPlans.stream().filter(p -> p.getPeriod().compareTo(period) < 0)
                .sorted(Comparator.comparing(BankLoanReplan::getPeriod).reversed()).toList();
        boolean triggerRecycle = isTriggerRecycle(loan.getPeriods(), period, historyList);
        return createClaimBankPlans(loan, period, triggerRecycle, repayPlans);
    }

    /**
     * 处理代还的记录
     *
     * @param bankConfig: 资方配置
     * @param plan:       当期还款计划
     * @return
     */
    protected BankBatchSubstituteRecord processSubstituteBankRecord(BankConfig bankConfig, LoanReplan plan) {
        logger.info("process substitute record, loanId: {}, period: {}", plan.getLoanId(), plan.getPeriod());
        Optional<BankBatchSubstituteRecord> record = commonService.findBankSubstituteRecordByLoanIdAndPeriod(plan.getLoanId(), plan.getPeriod());

        if (record.isPresent()) {
            logger.error("对资已代还, 对资还款计划已存在, loanId: {}, period: {}", plan.getLoanId(), plan.getPeriod());
            getWarningService().warn("代还对资还款计划已存在. loanId: " + plan.getLoanId() + ", period: " + plan.getPeriod());
            return null;
        }

        List<BankLoanReplan> historyList = getFinRepayService().getBankRepayPlans(plan.getLoanId()).stream()
                .filter(p -> p.getPeriod().compareTo(plan.getPeriod()) < 0).sorted(Comparator.comparing(BankLoanReplan::getPeriod).reversed()).toList();

        boolean isSubstitute = isSubstitute(bankConfig, plan, historyList);

        if (!isSubstitute) {
            return null;
        }

        return createSubstituteBankRecord(plan, bankConfig, historyList);
    }

    /**
     * 是否触发回购
     *
     * @param totalPeriod   总期数
     * @param currentPeriod 当前期数
     * @param plans         历史对资还款计划(<当前期数)
     * @return 是否触发回购规则
     */
    protected boolean isTriggerRecycle(Integer totalPeriod, Integer currentPeriod, List<BankLoanReplan> plans) {
        return false;
    }

    /**
     * 是否触发回购（连三累六）
     * <p>
     * 若借据最后一期触发回购，则最后一期依然为回购；
     * <p>
     * 如果需求为最后一期不为回购，则需要在调用当期方法之前判断；
     *
     * @param plans
     * @return
     */
    protected boolean continuousThreeOrAccumulatedSix(List<BankLoanReplan> plans) {
        // 对资还款计划小于两次，不可能触发回购
        if (plans.size() < 2) {
            return false;
        }

        // 最新两次为代偿还款，加上本次代偿还款，连续三次代偿还款，触发回购
        if (RepayType.CLAIM == plans.get(0).getRepayType() && RepayType.CLAIM == plans.get(1).getRepayType()) {
            return true;
        }

        // 计算所有代偿还款的总次数，当>=5时，加上本次代偿还款，触发回购
        long claimTimes = plans.stream().filter(bankLoanReplan -> RepayType.CLAIM.equals(bankLoanReplan.getRepayType())).count();
        return claimTimes >= NUM_FIVE;
    }

    /**
     * 是否需要代还
     *
     * @param plan: 当期还款计划
     * @return
     */
    protected boolean isSubstitute(BankConfig bankConfig, LoanReplan plan, List<BankLoanReplan> historyList) {

        Loan loan = getCommonService().findLoanById(plan.getLoanId());
        // 总期数小于等于3期，不允许代还
        if (loan.getPeriods().compareTo(NOT_ALLOW_PERIOD) <= 0) {
            return false;
        }

        // 倒数第几期不代还
        Integer lastPeriodUnsubstituted = loan.getPeriods() - bankConfig.getLastPeriodUnsubstituted() + 1;
        if (plan.getPeriod().compareTo(lastPeriodUnsubstituted) >= 0) {
            return false;
        }

        // 连续回购规则
        if (checkConsecutiveCompensation(bankConfig, historyList)) {
            return true;
        }

        // 累计回购规则
        if (checkCumulativeCompensation(bankConfig, historyList)) {
            return true;
        }

        // 逾期回购规则
        return checkFullCompensation(bankConfig, plan);
    }

    /**
     * 检查连续回购规则
     *
     * @param bankConfig  资方配置
     * @param historyList 已还计划
     * @return
     */
    private boolean checkConsecutiveCompensation(BankConfig bankConfig, List<BankLoanReplan> historyList) {
        Integer consecutiveCompensations = bankConfig.getConsecutiveCompensations();
        if (consecutiveCompensations < 0) {
            return false;
        }

        if (historyList.size() < consecutiveCompensations - 1) {
            return false;
        }

        for (int i = 0; i < consecutiveCompensations - 1; i++) {
            if (RepayType.CLAIM != historyList.get(i).getRepayType()) {
                return false;
            }
        }
        return true;
    }

    /**
     * 检查累计回购规则
     *
     * @param bankConfig  资方配置
     * @param historyList 已还计划
     * @return
     */
    private boolean checkCumulativeCompensation(BankConfig bankConfig, List<BankLoanReplan> historyList) {
        Integer cumulativeCompensations = bankConfig.getCumulativeCompensations();
        if (cumulativeCompensations < 0) {
            return false;
        }

        long claimTimes = historyList.stream().filter(bankLoanReplan -> RepayType.CLAIM.equals(bankLoanReplan.getRepayType())).count();
        return claimTimes >= cumulativeCompensations - 1;
    }

    /**
     * 检查逾期回购规则
     *
     * @param bankConfig 资方配置
     * @param plan       当期还款计划
     * @return
     */
    private boolean checkFullCompensation(BankConfig bankConfig, LoanReplan plan) {
        Integer fullCompensation = bankConfig.getFullCompensation();
        if (fullCompensation < 0) {
            return false;
        }
        long days = ChronoUnit.DAYS.between(plan.getRepayDate(), LocalDate.now());
        return days >= fullCompensation - bankConfig.getSubstituteAdvanceDays();
    }

    protected List<BankLoanReplan> createClaimBankPlans(final Loan loan, final Integer period, final boolean triggerRecycle, List<LoanReplan> repayPlans) {
        Integer loanPeriods = loan.getPeriods();
        String loanId = loan.getId();
        List<BankLoanReplan> result = new ArrayList<>();
        if (triggerRecycle) {
            for (var i = period; i <= loanPeriods; i++) {
                var r = createClaimClearBankPlan(loan.getChannel(), loanId, i, repayPlans);
                result.add(r);
            }
        } else {
            var r = createClaimSingleBankPlan(loan.getChannel(), loanId, period, repayPlans);
            result.add(r);
        }

        return result;
    }

    private BankLoanReplan createClaimSingleBankPlan(BankChannel channel, final String loanId, final Integer period, List<LoanReplan> repayPlans) {
        return createClaimBankPlan(channel, loanId, period, RepayPurpose.CURRENT, repayPlans);
    }

    private BankLoanReplan createClaimClearBankPlan(BankChannel channel, final String loanId, final Integer period, List<LoanReplan> repayPlans) {
        return createClaimBankPlan(channel, loanId, period, RepayPurpose.CLEAR, repayPlans);
    }

    protected BankLoanReplan createClaimBankPlan(BankChannel channel, final String loanId, final Integer period, RepayPurpose repayPurpose,
                                                 List<LoanReplan> repayPlans) {
        LoanReplan loanReplan = filterLoanReplan(repayPlans, period);

        BankLoanReplan bp = RepayConvert.INSTANCE.initClaimBankLoanReplan(loanReplan, repayPurpose);
        getFinRepayService().saveBankRepayPlan(bp);
        return bp;
    }

    protected BankBatchSubstituteRecord createSubstituteBankRecord(LoanReplan plan, BankConfig bankConfig, List<BankLoanReplan> plans) {
        // 代还记录
        BankBatchSubstituteRecord substituteRecord = RepayConvert.INSTANCE.initSubstituteBankRecord(plan);

        long claimCounts = plans.stream().filter(bankLoanReplan -> RepayType.CLAIM.equals(bankLoanReplan.getRepayType())).count();
        substituteRecord.setTotalCompensations(Math.toIntExact(claimCounts));

        int consecutiveCompensations = 0;
        for (int i = 0; i < bankConfig.getConsecutiveCompensations() && i < plans.size(); i++) {
            if (RepayType.CLAIM != plans.get(i).getRepayType()) {
                break;
            }
            consecutiveCompensations += 1;
        }
        substituteRecord.setConsecutiveCompensations(consecutiveCompensations);
        return substituteRecord;
    }

    protected LoanReplan filterLoanReplan(List<LoanReplan> replans, Integer period) {
        return replans.stream().filter(p -> p.getPeriod().compareTo(period) == 0).findAny().orElseThrow(()
                -> new BizException(BizErrorCode.REPAY_PERIOD_NOT_EXIST));
    }

    public BigDecimal safeNum(BigDecimal num) {
        return num == null ? BigDecimal.ZERO : num;
    }

    public abstract CompensatedRepaySyncRlt bankCompensatedRepaySync(CompensatedRepaySyncVo syncVo);

    public boolean batchApplyRepayValidate(RepayDeductionApplyVo repayApplyVo) {
        return true;
    }

    @Override
    public RepayResultVo batchApply(String batchRepayId) {
        RepayResultVo resultVo = new RepayResultVo();
        resultVo.setStatus(ProcessStatus.PROCESSING);
        resultVo.setRepayId(batchRepayId);
        // 异步还款申请
        mqService.submitBatchRepayApply(batchRepayId);
        return resultVo;
    }

    @Override
    public void batchRepayApply(BatchCustomerRepayRecord repayRecord) {
        // 还款申请
        RepayResultVo resultVo = batchGroupRepayApply(repayRecord);
        if (resultVo.getStatus().equals(ProcessStatus.PROCESSING)) {
            // mq查询还款结果
            getMqService().submitBatchRepayQueryDelay(repayRecord.getId());
        }
    }

    @Override
    public void batchRepayQuery(String batchRepayId) {
    }

    @Override
    public void bankRepayBatchNotify(BankProcessDTO processDTO) {
        BankChannel channel = processDTO.getChannel();
        LocalDate processDate = processDTO.getProcessDate();
        String businessId = processDTO.getBusinessId();
        if (StringUtil.isNotEmpty(businessId)) {
            mqService.submitRepayBankNotify(businessId);
            return;
        }

        List<CustomerRepayRecord> offlineRepayRecords = finRepayService.getCustomOfflineRepayRecords(channel, processDate);

        offlineRepayRecords.forEach(cr -> {
            Optional<BankRepayRecord> existBankRecord = finRepayService
                    .findBankRepayRecordsByLoanIdAndPeriodAndRepayStatus(cr.getLoanId(), cr.getPeriod(), ProcessStatus.SUCCESS);
            if (existBankRecord.isEmpty()) {
                logger.info("actual bank notify cust_record_id: {}, loan_id: {}", cr.getId(), cr.getLoanId());
                mqService.submitRepayBankNotify(cr.getId());
            }
        });

        BankRepayService.super.bankRepayBatchNotify(processDTO);
    }

    /**
     * 还款成功后的处理还款计划
     */
    protected void doBatchRepaySuccess(List<BatchCustomerRepayDetail> repayDetails, BatchCustomerRepayRecord repayRecord) {
        for (BatchCustomerRepayDetail repayDetail : repayDetails) {
            LoanReplan repayPlan = finRepayService.getRepayPlan(repayDetail.getLoanNo(), repayDetail.getPeriod());
            if (!getFinRepayService().existClaimedRepayPlan(repayDetail.getLoanNo(), repayDetail.getPeriod())) {
                repayPlan.setBankRepayStatus(RepayStatus.REPAID);
                // 对资方还款记录
                BankRepayRecord bankRepayRecord = finRepayService
                        .findByLoanIdAndPeriodAndRepayStatus(repayDetail.getLoanNo(), repayDetail.getPeriod(), ProcessStatus.SUCCESS);

                if (Objects.isNull(bankRepayRecord)) {
                    bankRepayRecord = RepayConvert.INSTANCE.toBankRepayEntity(repayDetail);
                    bankRepayRecord.setAgreementNo(repayRecord.getAgreementNo());
                    bankRepayRecord.setRepayBankCode(repayRecord.getRepayBankCode());
                    bankRepayRecord.setRepayAcctNo(repayRecord.getRepayAcctNo());
                    bankRepayRecord.setRepayRelUser(repayRecord.getRepayRelUser());
                    bankRepayRecord.setRepayRelPhone(repayRecord.getRepayRelPhone());
                    bankRepayRecord.setRepayRelCard(repayRecord.getRepayRelCard());
                    bankRepayRecord.setRepayStatus(ProcessStatus.SUCCESS);
                    finRepayService.saveBankRepayRecord(bankRepayRecord);
                }

                // 对资还款计划
                BankLoanReplan bankLoanReplan = getFinRepayService().findBankRepayPlan(repayDetail.getLoanNo(), repayDetail.getPeriod());
                if (Objects.isNull(bankLoanReplan)) {
                    bankLoanReplan = RepayConvert.INSTANCE.toPlanEntity(bankRepayRecord);
                    bankLoanReplan.setActBreachAmt(BigDecimal.ZERO);
                    bankLoanReplan.setActGuaranteeAmt(BigDecimal.ZERO);
                    bankLoanReplan.setId(repayPlan.getId());
                }
                bankLoanReplan.setRepayStatus(RepayStatus.REPAID);
                finRepayService.saveBankRepayPlan(bankLoanReplan);
            }

            if (RepayType.REFUND.equals(repayDetail.getRepayType()) || RepayMode.ONLINE.equals(repayDetail.getRepayMode())) {
                // 对客还款记录
                CustomerRepayRecord customerRepayRecord = RepayConvert.INSTANCE.repayDetailsToCustomerRepayRecord(repayDetail);
                customerRepayRecord.setOuterRepayId(repayRecord.getOuterRepayId());
                customerRepayRecord.setAgreementNo(repayRecord.getAgreementNo());
                customerRepayRecord.setRepayBankCode(repayRecord.getRepayBankCode());
                customerRepayRecord.setRepayAcctNo(repayRecord.getRepayAcctNo());
                customerRepayRecord.setRepayRelUser(repayRecord.getRepayRelUser());
                customerRepayRecord.setRepayRelPhone(repayRecord.getRepayRelPhone());
                customerRepayRecord.setRepayRelCard(repayRecord.getRepayRelCard());
                customerRepayRecord.setRepayStatus(ProcessStatus.SUCCESS);
                finRepayService.updateCustomRepayRecord(customerRepayRecord);

                // 对客还款计划
                CustomerLoanReplan customerLoanReplan = getFinRepayService().getCustomRepayPlan(repayDetail.getLoanNo(), repayDetail.getPeriod());
                if (Objects.isNull(customerLoanReplan)) {
                    customerLoanReplan = RepayConvert.INSTANCE.toPlanEntity(customerRepayRecord);
                    customerLoanReplan.setActBreachAmt(BigDecimal.ZERO);
                    customerLoanReplan.setActGuaranteeAmt(BigDecimal.ZERO);
                    customerLoanReplan.setId(repayPlan.getId());
                }
                customerLoanReplan.setRepayStatus(RepayStatus.REPAID);
                finRepayService.saveCustomerRepayPlan(customerLoanReplan);
                // 还款计划表增加资方罚息
                repayPlan.setPenaltyAmt(customerLoanReplan.getActPenaltyAmt());
                repayPlan.setCustRepayStatus(RepayStatus.REPAID);
            }
            finRepayService.updateRepayPlan(repayPlan);
        }
    }


    /***
     * 信托计划释放额度
     * @return
     */
    private void releaseTrust(BigDecimal amount, String trustPlanNo, String remark) {

    }


    @Override
    public BatchTrialResultVo batchTrial(BatchTrailVo trailVo) {
        return null;
    }

    @Override
    public void planOverdue(PlanOverdueVo planOverdueVo) {
    }

    @Override
    public void trustPlanRelease(TrustPlanReleaseVo trustPlanReleaseVo) {
    }

    @Override
    public DefrayResultVo defray(DefrayVo defrayVo) {
        return null;
    }

    @Override
    public SubstituteMarkResultVo substituteMark(SubstituteMarkApplyVo applyVo) {
        logger.info("substitute mark start, param applyVo: {}", JsonUtil.toJsonString(applyVo));

        BankConfig bankConfig = commonService
                .findBankConfigByChannelAndGuaranteeCompanyAndEnabled(applyVo.getBankChannel(), applyVo.getGuaranteeCompany(), WhetherState.Y)
                .orElseThrow(() -> new BizException(BizErrorCode.CHANNEL_CONFIG_NOT_FOUND));

        Integer consecutiveRule = bankConfig.getConsecutiveCompensations();
        Integer cumulativeRule = bankConfig.getCumulativeCompensations();
        Integer fullRule = bankConfig.getFullCompensation();

        if (areAllRulesNegative(consecutiveRule, cumulativeRule, fullRule)) {
            logger.info("substitute mark end, channel not allowed substitute");
            SubstituteMarkResultVo result = new SubstituteMarkResultVo();
            result.setStatus(ProcessStatus.FAIL);
            return result;
        }

        // 应还日
        LocalDate repayDate = applyVo.getSubstituteDay().plusDays(bankConfig.getSubstituteAdvanceDays()).minusDays(bankConfig.getClaimDays());
        List<LoanReplan> substitutePlans = getFinRepayService()
                .findRepayPlanBankNotPaidAndGuaranteeCompany(repayDate, applyVo.getBankChannel(), applyVo.getGuaranteeCompany());

        logger.info("need substitute mark count:{}", substitutePlans.size());

        for (LoanReplan lp : substitutePlans) {
            BankBatchSubstituteRecord record;
            try {
                record = processSubstituteBankRecord(bankConfig, lp);
                if (Objects.isNull(record)) {
                    continue;
                }
                record.setGuaranteeCompany(applyVo.getGuaranteeCompany());
                record.setRepayDate(repayDate.atStartOfDay());
                record.setClaimDate(repayDate.plusDays(bankConfig.getClaimDays()));
                commonService.saveBankSubstituteRecord(record);
            } catch (Exception e) {
                logger.error("代还标记异常", e);
                getWarningService().warn("代还标记异常,bankChannel:" + applyVo.getBankChannel() + "loan plan id:" + lp.getId());
            }
        }

        SubstituteMarkResultVo resultVo = new SubstituteMarkResultVo();
        resultVo.setStatus(ProcessStatus.SUCCESS);
        logger.info("substitute mark finished");
        return resultVo;
    }

    private boolean areAllRulesNegative(int consecutiveRule, int cumulativeRule, int fullRule) {
        return consecutiveRule < 0 && cumulativeRule < 0 && fullRule < 0;
    }

    @Override
    public SubstituteApplyResultVo substituteApply(SubstituteApplyVo substituteApplyVo) {
        logger.info("substitute apply start, param substituteApplyVo: {}", JsonUtil.toJsonString(substituteApplyVo));
        List<BankBatchSubstituteRecord> records = commonService
                .findWaitProcessSubstituteRecords(substituteApplyVo.getBankChannel(),
                        substituteApplyVo.getGuaranteeCompany(), substituteApplyVo.getSubstituteDay(), PushStatus.WAIT);
        records.forEach(record -> {
            Optional<CustomerRepayRecord> optional = commonService.findRepayRecordByLoanIdAndPeriodAndStatus(record.getLoanId(), record.getPeriod());
            if (optional.isEmpty()) {
                getMqService().submitSubstituteBankApply(record.getId());
            } else {
                record.setPushStatus(PushStatus.CANCEL);
                record.setFailReason("用户存在成功或处理中的当期还款记录");
                record.setRemark("用户存在成功或处理中的当期还款记录");
                commonService.saveBankSubstituteRecord(record);
            }
        });
        logger.info("substitute apply finished");
        return new SubstituteApplyResultVo();
    }

    @Override
    public SubstituteApplyResultVo handSubstituteApply(SubstituteApplyVo substituteApplyVo) {
        return null;
    }


    @Override
    public BatchTrialResultVo buildBatchCustomerTrialResult(Loan loan, RepayDeductionApplyVo repayApplyVo) {
        // 创建还款试算请求对象
        BatchTrailVo batchTrailVo = new BatchTrailVo();
        batchTrailVo.setChannel(loan.getChannel());
        batchTrailVo.setRepayPurpose(repayApplyVo.getRepayPurpose());
        batchTrailVo.setRepayType(repayApplyVo.getRepayType());
        batchTrailVo.setTotalReduceAmt(repayApplyVo.getTotalReduceAmt());
        // batchTrailVo.setTrailItems(GmxtPlanConvert.INSTANCE.toPlanTrailItemVo(repayApplyVo.getApplyList()));
        batchTrailVo.setApplyList(repayApplyVo.getApplyList());
        // 执行还款试算
        return batchTrial(batchTrailVo);
    }

    @Override
    public List<BankRepayRecordVo> queryBankRepayRecord(String loanNo, BatchCustomerRepayRecord repayRecord) {
        Loan loan = commonService.findLoanById(loanNo);
        // 先享卡账单模式,退款才返回对资的数据。需要组装数据为不缩期账单模式
        List<BankRepayRecord> bankRepayRecordList = commonService.findBankRepayRecordByLoanId(loan.getId());
        for (BankRepayRecord bankRepayRecord : bankRepayRecordList) {
            LoanReplan loanReplan = commonService.findByLoanIdAndPeriod(bankRepayRecord.getLoanId(), bankRepayRecord.getPeriod());
            bankRepayRecord.setPrincipalAmt(loanReplan.getPrincipalAmt());
            bankRepayRecord.setTotalAmt(AmountUtil.sum(bankRepayRecord.getPrincipalAmt(), bankRepayRecord.getInterestAmt()));
        }
        return RepayConvert.INSTANCE.toBatchRepayRecordList(bankRepayRecordList);
    }


    @Override
    public RepayResultVo batchGroupRepayApply(BatchCustomerRepayRecord repayRecord) {
        return null;
    }


    @Override
    public BankPlanQueryRltVo bankPlanQuery(BankPlanQueryVo planQueryVo) {
        BankPlanQueryRltVo bankPlanQueryRltVo = new BankPlanQueryRltVo();
        List<LoanReplan> repayPlans = getFinRepayService().getRepayPlans(planQueryVo.getLoanId());
        List<BankPlanItemVo> planItems = RepayConvert.INSTANCE.toPlanItems(repayPlans);
        planItems.forEach(p -> {
            boolean isClaim = getFinRepayService().existClaimedRepayPlan(p.getLoanId(), p.getPeriod());
            p.setIsMarkClaim(isClaim ? WhetherState.Y.name() : WhetherState.N.name());
        });
        bankPlanQueryRltVo.setBankPlanItems(planItems);
        return bankPlanQueryRltVo;
    }


    protected MqService getMqService() {
        return mqService;
    }

    protected FinRepayService getFinRepayService() {
        return finRepayService;
    }

    protected CommonService getCommonService() {
        return commonService;
    }

    public WarningService getWarningService() {
        return warningService;
    }

    @Autowired
    public void setWarningService(WarningService warningService) {
        this.warningService = warningService;
    }

    public InnerClaimMarkService getInnerClaimMarkService() {
        return innerClaimMarkService;
    }

    @Autowired
    public void setInnerClaimMarkService(InnerClaimMarkService innerClaimMarkService) {
        this.innerClaimMarkService = innerClaimMarkService;
    }

    @Override
    public ReccResultVo confirm(RccConfirmVo rccConfirmVo) {
        return null;
    }


    @Override
    public ReccDetailQueryResultVo queryRccDetail(ReccDetailQueryVo vo) {
        return null;
    }
}

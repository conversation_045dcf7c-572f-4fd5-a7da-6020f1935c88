package com.jinghang.capital.core.banks.hxbk.dto.contract;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.jinghang.capital.core.banks.hxbk.dto.HXBKBaseRequest;
import com.jinghang.capital.core.banks.hxbk.enums.HXBKMethod;

/**
 * @作者 Mr.sandman
 * @时间 2025/07/12 10:45
 */
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class HXBKContractGetRequest extends HXBKBaseRequest {

    @JsonIgnore
    private static final HXBKMethod METHOD = HXBKMethod.CONTRACT_GET;

    // 原授信/用信订单号
    @JsonProperty("original_order_no")
    private String originalOrderNo;

    // 客户号
    @JsonProperty("customer_no")
    private String customerNo;

    // 合同类型
    @JsonProperty("contract_type")
    private String contractType;

    public String getOriginalOrderNo() {
        return originalOrderNo;
    }

    public void setOriginalOrderNo(String originalOrderNo) {
        this.originalOrderNo = originalOrderNo;
    }

    public String getCustomerNo() {
        return customerNo;
    }

    public void setCustomerNo(String customerNo) {
        this.customerNo = customerNo;
    }

    public String getContractType() {
        return contractType;
    }

    public void setContractType(String contractType) {
        this.contractType = contractType;
    }

    @Override
    public HXBKMethod getMethod() {
        return METHOD;
    }
}

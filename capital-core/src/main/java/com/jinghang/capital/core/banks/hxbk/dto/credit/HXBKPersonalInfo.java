package com.jinghang.capital.core.banks.hxbk.dto.credit;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * HXBK个人基础信息
 *
 * @Author: Lior
 * @CreateTime: 2025/7/8 10:05
 */
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class HXBKPersonalInfo {

    /**
     * 客户姓名
     */
    @JsonProperty("custom_name")
    private String customName;

    /**
     * 身份证号码(18位)
     */
    @JsonProperty("card_no")
    private String cardNo;

    /**
     * 证件类型
     * 1-身份证
     */
    @JsonProperty("id_type")
    private String idType;

    /**
     * 证件开始日期
     * YYYY-MM-DD
     */
    @JsonProperty("cert_sign_date")
    private String certSignDate;

    /**
     * 身份证有效期
     * 格式：YYYY-MM-DD，长期: 9999-12-31
     */
    @JsonProperty("cert_validate")
    private String certValidate;

    /**
     * 证件地址
     * 身份证地址
     */
    @JsonProperty("cert_adr")
    private String certAdr;

    /**
     * 所在省份 汉字
     * 身份证地址
     */
    @JsonProperty("province")
    private String province;

    /**
     * 所在城市 汉字
     * 身份证地址
     */
    @JsonProperty("city")
    private String city;

    /**
     * 地区名称 汉字
     * 身份证地址
     */
    @JsonProperty("area")
    private String area;

    /**
     * 详细地址
     * 身份证地址
     */
    @JsonProperty("address")
    private String address;

    /**
     * 性别
     * M-男, F-女
     */
    @JsonProperty("sex")
    private String sex;

    /**
     * 民族 汉
     */
    @JsonProperty("nation")
    private String nation;

    /**
     * 手机号码
     */
    @JsonProperty("mobile")
    private String mobile;

    /**
     * 学历
     */
    @JsonProperty("education")
    private String education;

    /**
     * 婚姻状态
     * 01 未婚, 02 已婚, 03 离异, 04 丧偶, 99 未知
     */
    @JsonProperty("marital_status")
    private String maritalStatus;

    // Getter and Setter methods
    public String getCustomName() {
        return customName;
    }

    public void setCustomName(String customName) {
        this.customName = customName;
    }

    public String getCardNo() {
        return cardNo;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

    public String getIdType() {
        return idType;
    }

    public void setIdType(String idType) {
        this.idType = idType;
    }

    public String getCertSignDate() {
        return certSignDate;
    }

    public void setCertSignDate(String certSignDate) {
        this.certSignDate = certSignDate;
    }

    public String getCertValidate() {
        return certValidate;
    }

    public void setCertValidate(String certValidate) {
        this.certValidate = certValidate;
    }

    public String getCertAdr() {
        return certAdr;
    }

    public void setCertAdr(String certAdr) {
        this.certAdr = certAdr;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public String getNation() {
        return nation;
    }

    public void setNation(String nation) {
        this.nation = nation;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getEducation() {
        return education;
    }

    public void setEducation(String education) {
        this.education = education;
    }

    public String getMaritalStatus() {
        return maritalStatus;
    }

    public void setMaritalStatus(String maritalStatus) {
        this.maritalStatus = maritalStatus;
    }
}

package com.jinghang.capital.core.banks;

import com.jinghang.capital.core.exception.FinCoreSys;
import com.jinghang.common.sys.SysModule;

public enum FinCoreBankModule implements SysModule {
    UNKNOWN("99", "未知");

    private final String moduleCode;

    private final String moduleName;

    FinCoreBankModule(String moduleCode, String moduleName) {
        this.moduleCode = moduleCode;
        this.moduleName = moduleName;
    }

    @Override
    public String getModuleCode() {
        return moduleCode;
    }

    @Override
    public String getModuleName() {
        return moduleName;
    }

    @Override
    public FinCoreSys getSystem() {
        return FinCoreSys.INSTANCE;
    }
}

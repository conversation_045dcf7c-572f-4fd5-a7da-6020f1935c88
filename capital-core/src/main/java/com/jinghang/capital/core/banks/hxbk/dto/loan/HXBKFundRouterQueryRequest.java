package com.jinghang.capital.core.banks.hxbk.dto.loan;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.jinghang.capital.core.banks.hxbk.dto.HXBKBaseRequest;
import com.jinghang.capital.core.banks.hxbk.enums.HXBKMethod;

/**
 * 天枢系统资金方代码（资金路由）查询请求参数
 *
 * @Author: Lior
 * @CreateTime: 2025/7/7 20:19
 */
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class HXBKFundRouterQueryRequest extends HXBKBaseRequest {
    @JsonIgnore
    private static final HXBKMethod METHOD = HXBKMethod.FUND_ROUTER_QUERY;

    @Override
    public HXBKMethod getMethod() {
        return METHOD;
    }
}

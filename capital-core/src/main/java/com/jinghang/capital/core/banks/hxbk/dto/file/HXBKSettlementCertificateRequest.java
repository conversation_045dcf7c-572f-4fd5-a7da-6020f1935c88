package com.jinghang.capital.core.banks.hxbk.dto.file;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.jinghang.capital.core.banks.hxbk.dto.HXBKBaseRequest;
import com.jinghang.capital.core.banks.hxbk.enums.HXBKMethod;

/**
 * HXBK结清证明查询请求
 *
 * @Author: Lior
 * @CreateTime: 2025/7/14 9:54
 */
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class HXBKSettlementCertificateRequest extends HXBKBaseRequest {
    @JsonIgnore
    private static final HXBKMethod SETTLEMENT_CERTIFICATE_QUERY = HXBKMethod.SETTLEMENT_CERTIFICATE_QUERY;

    /**
     * 订单号
     */
    @JsonProperty("order_no")
    private String orderNo;

    /**
     * 授信/用信申请订单号
     */
    @JsonProperty("original_order_no")
    private String originalOrderNo;

    /**
     * 开具类型：01-授信，02-提现
     */
    @JsonProperty("scene")
    private String scene;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getOriginalOrderNo() {
        return originalOrderNo;
    }

    public void setOriginalOrderNo(String originalOrderNo) {
        this.originalOrderNo = originalOrderNo;
    }

    public String getScene() {
        return scene;
    }

    public void setScene(String scene) {
        this.scene = scene;
    }

    @Override
    public HXBKMethod getMethod() {
        return SETTLEMENT_CERTIFICATE_QUERY;
    }
}

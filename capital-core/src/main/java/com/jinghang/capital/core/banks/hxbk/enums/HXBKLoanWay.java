package com.jinghang.capital.core.banks.hxbk.enums;

import com.jinghang.capital.api.dto.LoanPurpose;
import com.jinghang.capital.core.enums.FlowChannel;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import org.springframework.util.CollectionUtils;

/**
 * 借款用途
 */
public enum HXBKLoanWay {

    //    PHONE(1, "手机数码",),
    //    RENT_HOUSE(6, "租房",LoanPurpose.OTHER),
    //    FURNITURE(7, "家具家居",LoanPurpose.SHOPPING),
    //    HOUSEHOLD_APP(10, "家用电器",Arrays.asList(LoanPurpose.SHOPPING,LoanPurpose.SHOPPING))
    TRAVEL(2, "旅游", new HashMap<>() {{
            put(FlowChannel.PPCJDL, List.of(LoanPurpose.TOUR));
            put(FlowChannel.LVXIN, List.of(LoanPurpose.TOUR));
        }}),
    DECORATION(3, "装修", new HashMap<>() {{
            put(FlowChannel.PPCJDL, List.of(LoanPurpose.DECORATION));
            put(FlowChannel.LVXIN, List.of(LoanPurpose.DECORATION));
        }}),
    EDU(4, "教育", new HashMap<>() {{
            put(FlowChannel.LVXIN, List.of(LoanPurpose.EDUCATION));
        }}),
    WEDDING(5, "婚庆", new HashMap<>() {{
            put(FlowChannel.PPCJDL, List.of(LoanPurpose.MARRIAGE));
            put(FlowChannel.LVXIN, List.of(LoanPurpose.MARRIAGE));
        }}),
    HEALTH(8, "健康医疗", new HashMap<>() {{
            put(FlowChannel.PPCJDL, List.of(LoanPurpose.HEALTH));
            put(FlowChannel.LVXIN, List.of(LoanPurpose.HEALTH));
        }}),
    OTHER_CONSUMPTION(9, "其他消费", new HashMap<>() {{
            put(FlowChannel.PPCJDL, Arrays.asList(LoanPurpose.SHOPPING, LoanPurpose.OTHER, LoanPurpose.EDUCATION));
            put(FlowChannel.LVXIN, Arrays.asList(LoanPurpose.SHOPPING, LoanPurpose.OTHER));
        }});
    private final Integer code;
    private final String desc;
    private final HashMap<FlowChannel, List<LoanPurpose>> loanPurpose;

    HXBKLoanWay(Integer code, String desc, HashMap<FlowChannel, List<LoanPurpose>> loanPurpose) {
        this.code = code;
        this.desc = desc;
        this.loanPurpose = loanPurpose;
    }


    /**
     * @param loanPurpose
     * @return
     */
    public static HXBKLoanWay getEnumByLoanPurpose(FlowChannel flowChannel, LoanPurpose loanPurpose) {
        return Arrays.stream(values()).filter(v -> {
            HashMap<FlowChannel, List<LoanPurpose>> purpose = v.getLoanPurpose();
            List<LoanPurpose> loanPurposes = purpose.get(flowChannel);
            if (!CollectionUtils.isEmpty(loanPurposes)) {
                return loanPurposes.contains(loanPurpose);
            } else {
                return false;
            }

            //            return v.getLoanPurpose().get(flowChannel).contains(loanPurpose)
        }).findFirst().orElse(OTHER_CONSUMPTION);

    }

    public HashMap<FlowChannel, List<LoanPurpose>> getLoanPurpose() {
        return loanPurpose;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}

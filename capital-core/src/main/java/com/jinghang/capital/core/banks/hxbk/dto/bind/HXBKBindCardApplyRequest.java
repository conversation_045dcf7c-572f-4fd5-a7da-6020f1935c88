package com.jinghang.capital.core.banks.hxbk.dto.bind;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.jinghang.capital.core.banks.hxbk.dto.HXBKBaseRequest;
import com.jinghang.capital.core.banks.hxbk.enums.HXBKMethod;


/**
 *
 */
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class HXBKBindCardApplyRequest extends HXBKBaseRequest {
    @JsonIgnore
    private static final HXBKMethod METHOD = HXBKMethod.BANKCARD_BIND;

    /**
     * 订单号
     */
    @JsonProperty("order_no")
    private String orderNo;

    /**
     * 渠道号
     */
    @JsonProperty("channel_code")
    private String channelCode;

    /**
     * 资产方用户唯一标识
     */
    @JsonProperty("open_id")
    private String openId;

    /**
     * 客户编号
     */
    @JsonProperty("customer_no")
    private String customerNo;

    /**
     * 客户名称 非必传
     */
    @JsonProperty("custom_name")
    private String customName;

    /**
     * 身份证 非必传
     */
    @JsonProperty("card_no")
    private String cardNo;

    /**
     * 手机号 非必传
     */
    @JsonProperty("mobile")
    private String mobile;

    /**
     * 银行卡号
     */
    @JsonProperty("bank_card_no")
    private String bankCardNo;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getChannelCode() {
        return channelCode;
    }

    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public String getCustomerNo() {
        return customerNo;
    }

    public void setCustomerNo(String customerNo) {
        this.customerNo = customerNo;
    }

    public String getCustomName() {
        return customName;
    }

    public void setCustomName(String customName) {
        this.customName = customName;
    }

    public String getCardNo() {
        return cardNo;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getBankCardNo() {
        return bankCardNo;
    }

    public void setBankCardNo(String bankCardNo) {
        this.bankCardNo = bankCardNo;
    }


    @Override
    public HXBKMethod getMethod() {
        return METHOD;
    }
}

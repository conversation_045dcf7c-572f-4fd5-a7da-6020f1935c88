package com.jinghang.capital.core.banks.hxbk.callback.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.jinghang.capital.core.banks.hxbk.dto.credit.HXBKCreditAmount;
import java.math.BigDecimal;
import java.util.Date;

/**
 * HXBK授信结果回调请求数据
 * 根据接口文档定义的请求参数结构
 *
 * @Author: Lior
 * @CreateTime: 2025/7/9
 */
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class CreditResultCallbackRequest {

    /**
     * 授信申请订单号
     */
    @JsonProperty("order_no")
    private String orderNo;

    /**
     * 业务类型
     * 1：现金贷 2：分期付
     */
    @JsonProperty("prod_type")
    private String prodType;

    /**
     * 资产方用户唯一标识
     */
    @JsonProperty("open_id")
    private String openId;

    /**
     * 授信状态
     * 0-通过 1-不通过 2-处理中
     */
    @JsonProperty("status")
    private String status;

    /**
     * 拒绝原因
     */
    @JsonProperty("msg")
    private String msg;

    /**
     * 授信额度
     */
    @JsonProperty("credit_amt")
    private BigDecimal creditAmt;

    /**
     * 期数
     */
    @JsonProperty("period")
    private Integer period;

    /**
     * 还款方式
     */
    @JsonProperty("repay_type")
    private String repayType;

    /**
     * 额度信息
     */
    @JsonProperty("credit_info")
    private HXBKCreditAmount creditInfo;

    /**
     * 用户编号
     */
    @JsonProperty("custom_no")
    private String customNo;

    /**
     * 授信申请编号
     */
    @JsonProperty("apply_no")
    private String applyNo;

    /**
     * 资金方编号
     */
    @JsonProperty("fund_code")
    private String fundCode;

    /**
     * 冷静期结束日期
     * 授信拒绝时返回
     */
    @JsonProperty("cooling_period")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date coolingPeriod;

    /**
     * 资金源编码
     * 下游资金方有多个资金来源时返回
     */
    @JsonProperty("loan_inst_code")
    private String loanInstCode;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getProdType() {
        return prodType;
    }

    public void setProdType(String prodType) {
        this.prodType = prodType;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public BigDecimal getCreditAmt() {
        return creditAmt;
    }

    public void setCreditAmt(BigDecimal creditAmt) {
        this.creditAmt = creditAmt;
    }

    public Integer getPeriod() {
        return period;
    }

    public void setPeriod(Integer period) {
        this.period = period;
    }

    public String getRepayType() {
        return repayType;
    }

    public void setRepayType(String repayType) {
        this.repayType = repayType;
    }

    public HXBKCreditAmount getCreditInfo() {
        return creditInfo;
    }

    public void setCreditInfo(HXBKCreditAmount creditInfo) {
        this.creditInfo = creditInfo;
    }

    public String getCustomNo() {
        return customNo;
    }

    public void setCustomNo(String customNo) {
        this.customNo = customNo;
    }

    public String getApplyNo() {
        return applyNo;
    }

    public void setApplyNo(String applyNo) {
        this.applyNo = applyNo;
    }

    public String getFundCode() {
        return fundCode;
    }

    public void setFundCode(String fundCode) {
        this.fundCode = fundCode;
    }

    public Date getCoolingPeriod() {
        return coolingPeriod;
    }

    public void setCoolingPeriod(Date coolingPeriod) {
        this.coolingPeriod = coolingPeriod;
    }

    public String getLoanInstCode() {
        return loanInstCode;
    }

    public void setLoanInstCode(String loanInstCode) {
        this.loanInstCode = loanInstCode;
    }
}

package com.jinghang.capital.core.banks.hxbk.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.jinghang.capital.core.banks.hxbk.enums.HXBKMethod;

/**
 * 湖消蚂蚁天枢基础请求类
 *
 * @Author: Lior
 * @CreateTime: 2025/7/7 20:18
 */
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public abstract class HXBKBaseRequest {
    @JsonIgnore
    public abstract HXBKMethod getMethod();
}

package com.jinghang.capital.core.banks.hxbk.recc;

import com.jinghang.capital.api.dto.BankChannel;
import com.jinghang.capital.core.banks.cybk.enums.CYBKCreditStatus;
import com.jinghang.capital.core.banks.cybk.enums.CYBKReccFileType;
import com.jinghang.capital.core.entity.CYBKReconcileFile;
import com.jinghang.capital.core.enums.ProcessStatus;
import com.jinghang.capital.core.repository.CYBKReconcileFileRepository;
import com.jinghang.capital.core.vo.recc.ReccResultVo;
import com.jinghang.capital.core.vo.recc.ReccType;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @作者 Mr.sandman
 * @时间 2025/06/26 09:45
 */
@Component
public class HXBKReconcileFileLoanQueryHandler implements HXBKReccQueryHandler {

    private static final Logger logger = LoggerFactory.getLogger(HXBKReconcileFileLoanQueryHandler.class);

    @Autowired
    private CYBKReconcileFileRepository cybkReconcileFileRepository;

    /**
     * 查询 对账文件
     *
     * @param date 对账时间
     * @return 对账结果
     */
    @Override
    public ReccResultVo query(LocalDate date) {
        ReccResultVo reccResultVo = new ReccResultVo();
        logger.info("查询资方放款对账文件参数 channel: {}, date: {}, fileType: {}", BankChannel.HXBK.name(), date, CYBKReccFileType.LOAN_FILE.name());
        List<CYBKReconcileFile> fileList = cybkReconcileFileRepository.
                findByChannelAndFileDateAndReccType(BankChannel.HXBK.name(), date, CYBKReccFileType.LOAN_FILE.name());
        logger.info("查询资方放款对账文件数量:{}", fileList.size());
        boolean isValid = Optional.of(fileList).filter(list -> !list.isEmpty())
                .map(list -> list.stream().anyMatch(file -> CYBKCreditStatus.SUCCESS.getCode().equals(file.getReccState()))).orElse(false);

        if (isValid) {
            reccResultVo.setStatus(ProcessStatus.SUCCESS);
            reccResultVo.setActReccTime(LocalDateTime.now());
        } else {
            reccResultVo.setStatus(ProcessStatus.FAIL);
            reccResultVo.setFailMsg("对资对账对账文件未完成或失败");
        }
        return reccResultVo;
    }

    /**
     * 对账类型
     *
     * @return 对账类型
     */
    @Override
    public ReccType getReccType() {
        return ReccType.LOAN;
    }
}

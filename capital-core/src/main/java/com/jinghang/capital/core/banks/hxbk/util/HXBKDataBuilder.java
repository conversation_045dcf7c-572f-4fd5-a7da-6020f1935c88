package com.jinghang.capital.core.banks.hxbk.util;

import com.jinghang.capital.api.dto.LoanPurpose;
import com.jinghang.capital.core.banks.hxbk.dto.credit.HXBKContact;
import com.jinghang.capital.core.banks.hxbk.dto.credit.HXBKJobInfo;
import com.jinghang.capital.core.banks.hxbk.dto.credit.HXBKLiveInfo;
import com.jinghang.capital.core.banks.hxbk.dto.credit.HXBKMaterial;
import com.jinghang.capital.core.banks.hxbk.dto.credit.HXBKPersonalInfo;
import com.jinghang.capital.core.banks.hxbk.dto.credit.HXBKRiskData;
import com.jinghang.capital.core.banks.hxbk.enums.HXBKContactType;
import com.jinghang.capital.core.banks.hxbk.enums.HXBKEducation;
import com.jinghang.capital.core.banks.hxbk.enums.HXBKIndustry;
import com.jinghang.capital.core.banks.hxbk.enums.HXBKLoanPurpose;
import com.jinghang.capital.core.banks.hxbk.enums.HXBKMarriage;
import com.jinghang.capital.core.banks.hxbk.enums.HXBKOccupation;
import com.jinghang.capital.core.entity.Account;
import com.jinghang.capital.core.entity.AccountContactInfo;
import com.jinghang.capital.core.entity.Credit;
import com.jinghang.capital.core.enums.Education;
import com.jinghang.capital.core.enums.Marriage;
import com.jinghang.capital.core.enums.Relation;
import com.jinghang.common.util.CollectionUtil;
import com.jinghang.common.util.JsonUtil;
import com.jinghang.common.util.StringUtil;
import java.math.BigDecimal;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * HXBK数据构建工具类
 *
 * @Author: Lior
 * @CreateTime: 2025/7/8 11:30
 */
public class HXBKDataBuilder {

    private static final Logger logger = LoggerFactory.getLogger(HXBKDataBuilder.class);

    /**
     * 构建个人信息
     *
     * @param account 账户信息
     * @return 个人信息
     */
    public static HXBKPersonalInfo buildPersonalInfo(Account account) {
        HXBKPersonalInfo personalInfo = new HXBKPersonalInfo();

        personalInfo.setCustomName(account.getName());
        personalInfo.setCardNo(account.getCertNo());
        // 身份证
        personalInfo.setIdType("1");

        // 证件日期格式化
        if (account.getCertValidStart() != null) {
            personalInfo.setCertSignDate(account.getCertValidStart().format(DateTimeFormatter.ISO_LOCAL_DATE));
        }
        if (account.getCertValidEnd() != null) {
            personalInfo.setCertValidate(account.getCertValidEnd().format(DateTimeFormatter.ISO_LOCAL_DATE));
        }

        // 地址信息
        if (account.getCertAddress() != null) {
            personalInfo.setCertAdr(account.getCertAddress());
            // 省份、城市、地区需要汉字，从身份证地址截取
            AddressUtil.AddressInfo addressInfo = AddressUtil.addressResolution(account.getCertAddress());
            personalInfo.setProvince(addressInfo.getProvince());
            personalInfo.setCity(addressInfo.getCity());
            personalInfo.setArea(addressInfo.getArea());
            personalInfo.setAddress(addressInfo.getDetail());
        }

        // 性别转换
        if (account.getGender() != null) {
            personalInfo.setSex("MALE".equals(account.getGender().name()) ? "M" : "F");
        } else {
            // 默认男性
            personalInfo.setSex("M");
        }

        String nation = account.getNation();
        if (StringUtil.isNotBlank(nation)) {
            nation = nation.replace("族", "");
        }
        personalInfo.setNation(nation);
        personalInfo.setMobile(account.getMobile());
        personalInfo.setEducation(convertEducation(account.getEducation()));
        personalInfo.setMaritalStatus(convertMarriage(account.getMarriage()));

        return personalInfo;
    }

    /**
     * 学历转换
     *
     * @param education 学历枚举
     * @return HXBK学历编码
     */
    public static String convertEducation(Education education) {
        return HXBKEducation.getCodeByEducation(education);
    }

    /**
     * 婚姻字典转换
     *
     * @param marriage 婚姻状态枚举
     * @return HXBK婚姻状态编码
     */
    public static String convertMarriage(Marriage marriage) {
        return HXBKMarriage.getEnumByMarriage(marriage).getCode();
    }

    /**
     * 借款原因转换
     *
     * @param loanPurpose 借款原因枚举
     * @return HXBK借款原因编码
     */
    public static String convertLoanPurpose(LoanPurpose loanPurpose) {
        return HXBKLoanPurpose.getCodeByLoanPurpose(loanPurpose);
    }

    /**
     * 构建工作信息
     *
     * @param account 账户信息
     * @return 工作信息
     */
    public static HXBKJobInfo buildJobInfo(Account account) {
        HXBKJobInfo jobInfo = new HXBKJobInfo();

        // 职业类型
        if (StringUtil.isNotBlank(account.getPosition())) {
            jobInfo.setJobType(HXBKOccupation.getHXBKCodeByPosition(account.getPosition()));
        }
        // 公司行业类型
        if (StringUtil.isNotBlank(account.getIndustry())) {
            jobInfo.setCompanyIndustryType(HXBKIndustry.getHXBKCodeByIndustry(account.getIndustry()));
        }
        if (StringUtil.isNotBlank(account.getUnitProvinceCode())) {
            jobInfo.setCompanyProvinceCode(account.getUnitProvinceCode());
        }
        if (StringUtil.isNotBlank(account.getUnitCityCode())) {
            jobInfo.setCompanyCityCode(account.getUnitCityCode());
        }
        if (StringUtil.isNotBlank(account.getUnitDistrictCode())) {
            jobInfo.setCompanyAreaCode(account.getUnitDistrictCode());
        }
        if (StringUtil.isNotBlank(account.getUnitAddress())) {
            jobInfo.setCompanyAddress(account.getUnitAddress());
        }
        if (StringUtil.isNotBlank(account.getUnit())) {
            jobInfo.setCompanyName(account.getUnit());
        }
        return jobInfo;
    }

    /**
     * 构建居住信息
     *
     * @param account 账户信息
     * @return 居住信息
     */
    public static HXBKLiveInfo buildLiveInfo(Account account) {
        HXBKLiveInfo liveInfo = new HXBKLiveInfo();

        if (StringUtil.isNotBlank(account.getLivingAddress())) {
            liveInfo.setLiveAddress(account.getLivingAddress());
        }
        if (StringUtil.isNotBlank(account.getLivingProvinceCode())) {
            liveInfo.setLiveProvince(account.getLivingProvinceCode());
        }
        if (StringUtil.isNotBlank(account.getLivingCityCode())) {
            liveInfo.setLiveCity(account.getLivingCityCode());
        }
        if (StringUtil.isNotBlank(account.getLivingDistrictCode())) {
            liveInfo.setLiveArea(account.getLivingDistrictCode());
        }
        if (StringUtil.isNotBlank(account.getLivingStreet())) {
            liveInfo.setLiveStreet(account.getLivingStreet());
        }
        return liveInfo;
    }

    /**
     * 构建联系人信息
     *
     * @param contactInfos 联系人信息列表
     * @return 联系人信息列表
     */
    public static List<HXBKContact> buildContacts(List<AccountContactInfo> contactInfos) {
        List<HXBKContact> contacts = new ArrayList<>();

        if (CollectionUtil.isNotEmpty(contactInfos)) {
            for (AccountContactInfo contactInfo : contactInfos) {
                HXBKContact contact = new HXBKContact();
                contact.setName(contactInfo.getName());
                contact.setMobile(contactInfo.getPhone());

                // 联系人类型转换
                String kind = convertContactType(contactInfo.getRelation());
                contact.setKind(kind);

                contacts.add(contact);
            }
        } else {
            logger.error("联系人信息为空");
        }

        return contacts;
    }

    /**
     * 构建风险数据JSON字符串
     *
     * @param credit       授信记录
     * @param contactInfos 联系人信息
     * @return 风险数据JSON字符串
     */
    public static String buildRiskDataJson(Credit credit, List<AccountContactInfo> contactInfos) {
        HXBKRiskData riskData = new HXBKRiskData();

        // 联系人信息
        riskData.setContacts(buildContacts(contactInfos));

        // 借据金额（转换为分）
        if (credit.getLoanAmt() != null) {
            riskData.setLoanAmount(credit.getLoanAmt().multiply(new BigDecimal("100")).longValue());
        }

        // 提现利率 精确到小数点后4位。暂定 0.2399
        riskData.setCreditRate(credit.getBankRate().doubleValue());

        return JsonUtil.toJsonString(riskData);
    }

    /**
     * 构建材料文件列表
     *
     * @param credit 授信记录
     * @return 材料文件列表
     */
    public static List<HXBKMaterial> buildMaterials(Credit credit) {
        List<HXBKMaterial> materials = new ArrayList<>();

        // 这里可以根据实际需要添加材料文件
        // 暂时返回空列表

        return materials;
    }

    /**
     * 转换联系人类型
     *
     * @param relation 关系枚举
     * @return HXBK联系人类型编码
     */
    public static String convertContactType(Relation relation) {
        return HXBKContactType.getEnumByRelation(relation).getCode();
    }
}

package com.jinghang.capital.core.banks;


import com.jinghang.capital.core.service.ChannelSupport;

public interface BankCallbackService extends ChannelSupport {

    /**
     * 授信结果回调
     *
     * @param json
     */
    String creditResultCallback(String json);

    /**
     * 放款结果回调
     *
     * @param json
     */
    String loanResultCallback(String json);

    /**
     * 调额结果回调
     *
     * @param json
     */
    String quotaAdjustResultCallback(String json);

    /**
     * 还款结果回调
     *
     * @param json
     */
    String repayResultCallback(String json);

    /**
     * 资方对账通知
     *
     * @param json
     */
    String rccResultCallback(String json);

}

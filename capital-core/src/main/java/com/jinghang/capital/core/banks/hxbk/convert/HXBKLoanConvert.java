package com.jinghang.capital.core.banks.hxbk.convert;

import com.jinghang.capital.core.banks.hxbk.callback.dto.LoanApplyResultCallbackRequest;
import com.jinghang.capital.core.banks.hxbk.dto.loan.HXBKLoanQueryResponse;
import com.jinghang.capital.core.banks.hxbk.dto.loan.HXBKRepayList;
import com.jinghang.capital.core.enums.ProcessStatus;
import com.jinghang.capital.core.vo.loan.LoanResultVo;
import com.jinghang.capital.core.vo.repay.PlanItemVo;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @date 2023/8/20
 */
@Mapper(uses = {HXBKEnumConvert.class})
public interface HXBKLoanConvert {
    HXBKLoanConvert INSTANCE = Mappers.getMapper(HXBKLoanConvert.class);


    @Mapping(source = "status", target = "status", qualifiedByName = "toProcessStatus")
    @Mapping(source = "msg", target = "failMsg")
    @Mapping(source = "receiptInfo.receiptNo", target = "loanNo")
    @Mapping(source = "contractNo", target = "loanContractNo")
    @Mapping(source = "receiptInfo.loanTime", target = "loanTime", dateFormat = "yyyy-MM-dd HH:mm:ss")
    LoanResultVo toVo(LoanApplyResultCallbackRequest response);


    @Mapping(source = "status", target = "status", qualifiedByName = "toProcessStatus")
    @Mapping(source = "msg", target = "failMsg")
    @Mapping(source = "receiptInfo.receiptNo", target = "loanNo")
    @Mapping(source = "disburseContractNo", target = "loanContractNo")
    @Mapping(source = "receiptInfo.loanTime", target = "loanTime", dateFormat = "yyyy-MM-dd HH:mm:ss")
    LoanResultVo toVo(HXBKLoanQueryResponse response);


    // List转换会自动使用下面的单对象转换方法
    List<PlanItemVo> toPlanVo(List<HXBKRepayList> repayPlanInfos);

    @Mapping(source = "period", target = "period")
    @Mapping(source = "settleDate", target = "repayDate", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Mapping(source = "needCorpus", target = "principalAmt")
    @Mapping(source = "needAccrual", target = "interestAmt")
    @Mapping(source = "needFee", target = "penaltyAmt")
    @Mapping(source = "needLiquidatedDamages", target = "breachAmt")
    @Mapping(source = "needGuaranteeFee", target = "guaranteeAmt")
    @Mapping(source = "needServiceCharge", target = "consultAmt")
    @Mapping(source = "alreadyAmount", target = "actTotalAmt")
    @Mapping(source = "alreadyCorpus", target = "actPrincipalAmt")
    @Mapping(source = "alreadyAccrual", target = "actInterestAmt")
    @Mapping(source = "alreadyPunish", target = "actPenaltyAmt")
    @Mapping(source = "alreadyLiquidatedDamages", target = "actBreachAmt")
    @Mapping(source = "alreadyGuaranteeFee", target = "actGuaranteeAmt")
    @Mapping(source = "alreadyServiceCharge", target = "actConsultAmt")
    @Mapping(source = "tradeDate", target = "actRepayDate", dateFormat = "yyyy-MM-dd HH:mm:ss")
    PlanItemVo repayToPlanItemVo(HXBKRepayList repay);


    @Mapping(source = "orderNo", target = "loanNo")
    @Mapping(source = "contractNo", target = "loanContractNo")
    @Mapping(source = "receiptInfo.loanAmount", target = "loanAmt")
    @Mapping(source = "receiptInfo.period", target = "period")
    @Mapping(source = "receiptInfo.loanTime", target = "loanTime")
    @Mapping(source = "msg", target = "failMsg")
    @Mapping(source = "status", target = "status", qualifiedByName = "toProcessStatus")
    LoanResultVo toCallbackVo(LoanApplyResultCallbackRequest request);


    @Named("toProcessStatus")
    default ProcessStatus toProcessStatus(String hxbkStatus) {
        return switch (hxbkStatus) {
            case "0" -> ProcessStatus.SUCCESS;
            case "1" -> ProcessStatus.FAIL;
            case "2" -> ProcessStatus.PROCESSING;
            default -> throw new IllegalArgumentException("Unexpected response status:" + hxbkStatus);
        };
    }

}

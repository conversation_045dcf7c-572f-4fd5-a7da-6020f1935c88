package com.jinghang.capital.core.banks.hxbk.dto.repay;


import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.jinghang.capital.core.banks.hxbk.dto.HXBKBaseRequest;
import com.jinghang.capital.core.banks.hxbk.enums.HXBKMethod;


@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class HXBKRepayTrialRequest extends HXBKBaseRequest {
    @JsonIgnore
    private static final HXBKMethod REPAY_TRIAL = HXBKMethod.REPAY_TRIAL;

    /**
     * 订单号
     */
    @JsonProperty("order_no")
    private String orderNo;
    /**
     * 用信申请订单号
     */
    @JsonProperty("original_order_no")
    private String originalOrderNo;
    /**
     * 还款类型(1: 全部结清；2：正常还款 3：当期结清 4：逾期还款)
     */
    @JsonProperty("repay_type")
    private String repayType;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getOriginalOrderNo() {
        return originalOrderNo;
    }

    public void setOriginalOrderNo(String originalOrderNo) {
        this.originalOrderNo = originalOrderNo;
    }

    public String getRepayType() {
        return repayType;
    }

    public void setRepayType(String repayType) {
        this.repayType = repayType;
    }

    @Override
    public HXBKMethod getMethod() {
        return REPAY_TRIAL;
    }
}

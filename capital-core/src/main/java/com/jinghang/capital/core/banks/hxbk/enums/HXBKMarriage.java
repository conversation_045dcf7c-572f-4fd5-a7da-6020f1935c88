package com.jinghang.capital.core.banks.hxbk.enums;

import com.jinghang.capital.core.enums.Marriage;
import java.util.Arrays;

/**
 * HXBK婚姻状态枚举
 * <p>
 * 码值映射
 * 01 未婚——未婚:UNMARRIED,
 * 02 已婚——已婚:MARRIED,
 * 03 离异——离异:DIVORCED，
 * 04 丧偶——丧偶:WIDOWED,
 * 99 未知——未知:UNKNOWN
 *
 * @Author: Lior
 * @CreateTime: 2025/7/8 15:30
 */
public enum HXBKMarriage {
    UNMARRIED("01", "未婚", Marriage.UNMARRIED),
    MARRIED("02", "已婚", Marriage.MARRIED),
    DIVORCED("03", "离异", Marriage.DIVORCED),
    WIDOWED("04", "丧偶", Marriage.WIDOWED),
    UNKNOWN("99", "未知", Marriage.UNKNOWN);

    private final String code;
    private final String desc;
    private final Marriage marriage;

    HXBKMarriage(String code, String desc, Marriage marriage) {
        this.code = code;
        this.desc = desc;
        this.marriage = marriage;
    }

    /**
     * 根据通用婚姻状态枚举获取HXBK婚姻状态枚举
     *
     * @param marriage 通用婚姻状态枚举
     * @return HXBK婚姻状态枚举
     */
    public static HXBKMarriage getEnumByMarriage(Marriage marriage) {
        return Arrays.stream(values())
                .filter(hxbkMarriage -> marriage.equals(hxbkMarriage.marriage))
                .findFirst()
                .orElse(UNKNOWN);
    }

    /**
     * 根据HXBK编码获取枚举
     *
     * @param code HXBK婚姻状态编码
     * @return HXBK婚姻状态枚举
     */
    public static HXBKMarriage getEnumByCode(String code) {
        return Arrays.stream(values())
                .filter(hxbkMarriage -> code.equals(hxbkMarriage.code))
                .findFirst()
                .orElse(UNKNOWN);
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public Marriage getMarriage() {
        return marriage;
    }
}

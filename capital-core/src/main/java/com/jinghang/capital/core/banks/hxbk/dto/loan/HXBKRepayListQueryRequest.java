package com.jinghang.capital.core.banks.hxbk.dto.loan;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.jinghang.capital.core.banks.hxbk.dto.HXBKBaseRequest;
import com.jinghang.capital.core.banks.hxbk.enums.HXBKMethod;

@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class HXBKRepayListQueryRequest extends HXBKBaseRequest {

    @JsonIgnore
    private static final HXBKMethod METHOD = HXBKMethod.REPAY_LIST_QUERY;
    /**
     * request 请求单号，每次请求唯一，如 uuid
     */
    @JsonProperty("order_no")
    private String orderNo;

    /**
     * 业务类型 Y 1：现金贷（默认） 2：分期付
     */
    @JsonProperty("prod_type")
    private String prodType;

    /**
     * 资产方订单号 STRING C prod_type=1时，用信申请的订单号
     */
    @JsonProperty("original_order_no")
    private String originalOrderNo;

    //资产方购物订单号 STRING C 购物订单号，如二轮车/摩托车订单号
    @JsonProperty("biz_order_no")
    private String bizOrderNo;


    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getProdType() {
        return prodType;
    }

    public void setProdType(String prodType) {
        this.prodType = prodType;
    }

    public String getOriginalOrderNo() {
        return originalOrderNo;
    }

    public void setOriginalOrderNo(String originalOrderNo) {
        this.originalOrderNo = originalOrderNo;
    }

    public String getBizOrderNo() {
        return bizOrderNo;
    }

    public void setBizOrderNo(String bizOrderNo) {
        this.bizOrderNo = bizOrderNo;
    }

    @Override
    public HXBKMethod getMethod() {
        return METHOD;
    }
}

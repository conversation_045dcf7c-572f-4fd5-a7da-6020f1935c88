package com.jinghang.capital.core.banks.hxbk.config;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializerProvider;
import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2025-07-08 16:48
 */
public class GenericJsonSerializer extends JsonSerializer<Object> {
    private static final ObjectMapper MAPPER = new ObjectMapper();

    @Override
    public void serialize(Object value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        String json = MAPPER.writeValueAsString(value);
        gen.writeRawValue(json);
    }
}


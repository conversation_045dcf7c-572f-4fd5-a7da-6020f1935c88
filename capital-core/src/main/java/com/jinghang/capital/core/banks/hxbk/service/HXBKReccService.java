package com.jinghang.capital.core.banks.hxbk.service;

import com.jinghang.capital.core.banks.BankReccService;
import com.jinghang.capital.core.banks.hxbk.recc.HXBKReccHandler;
import com.jinghang.capital.core.banks.hxbk.recc.HXBKReccQueryHandler;
import com.jinghang.capital.core.entity.CYBKReconcileFile;
import com.jinghang.capital.core.enums.BankChannel;
import com.jinghang.capital.core.vo.recc.ReccApplyVo;
import com.jinghang.capital.core.vo.recc.ReccDownloadVo;
import com.jinghang.capital.core.vo.recc.ReccFileApplyVo;
import com.jinghang.capital.core.vo.recc.ReccResultVo;
import com.jinghang.capital.core.vo.recc.ReccType;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @作者 Mr.sandman
 * @时间 2025/07/10 17:58
 */
@Service
public class HXBKReccService implements BankReccService, InitializingBean {

    private static final Logger logger = LoggerFactory.getLogger(HXBKReccService.class);

    @Autowired
    private List<HXBKReccHandler> reccHandlers;

    private Map<ReccType, HXBKReccHandler> reccHandlerMap;

    @Autowired
    private List<HXBKReccQueryHandler> reccQueryHandlers;

    private Map<ReccType, HXBKReccQueryHandler> reccQueryHandlerMap;

    @Override
    public void process(ReccApplyVo apply) {
        logger.info("开始处理 HXBK 对账. reccDay: [{}], type: [{}]", apply.getReccDay(), apply.getReccType());
        reccHandlerMap.get(apply.getReccType()).process(apply.getReccDay());
    }

    @Override
    public ReccResultVo query(ReccApplyVo apply) {
        logger.info("开始查询 HXBK 对账 reccDay: [{}], type: [{}]", apply.getReccDay(), apply.getReccType());
        return reccQueryHandlerMap.get(apply.getReccType()).query(apply.getReccDay());
    }

    @Override
    public ReccResultVo download(ReccDownloadVo vo) {
        return null;
    }

    @Override
    public List<CYBKReconcileFile> fileApply(ReccFileApplyVo vo) {
        return null;
    }

    /**
     * 是否支持该资方渠道
     *
     * @param channel 资方渠道
     * @return 支持情况
     */
    @Override
    public boolean isSupport(BankChannel channel) {
        return BankChannel.HXBK == channel;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        reccHandlerMap = reccHandlers.stream().collect(Collectors.toMap(HXBKReccHandler::getReccType, Function.identity()));
        reccQueryHandlerMap = reccQueryHandlers.stream().collect(Collectors.toMap(HXBKReccQueryHandler::getReccType, Function.identity()));

    }
}

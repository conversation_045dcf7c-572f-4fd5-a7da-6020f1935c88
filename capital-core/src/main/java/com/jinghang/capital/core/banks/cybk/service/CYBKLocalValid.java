package com.jinghang.capital.core.banks.cybk.service;


import com.jinghang.capital.core.banks.cybk.config.CYBKConfig;
import com.jinghang.capital.core.util.AgeUtil;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2023/8/23
 */
@Service
public class CYBKLocalValid {

    private static final LocalDate ID_CARD_EXPIRE_PERMANENT = LocalDate.of(2099, 1, 1);
    private static final LocalDate CYBK_PERMANENT = LocalDate.of(2100, 12, 31);

    private static final DateTimeFormatter VALID_FORMAT = DateTimeFormatter.ofPattern("HHmm");
    private static final DateTimeFormatter AGE_FORMAT = DateTimeFormatter.ofPattern("yyyyMMdd");

    private static final int ID_AGE_START = 6;
    private static final int ID_AGE_END = 14;


    private static final int AGE_LOWER_LIMIT = 22;
    private static final int AGE_HIGH_LIMIT = 55;


    private static final BigDecimal AMOUNT_LOWER_LIMIT = new BigDecimal("500");
    private static final BigDecimal AMOUNT_HIGH_LIMIT = new BigDecimal("50000");

    private static final ArrayList<Integer> PERIODS_RANGE = new ArrayList<>(Arrays.asList(3, 6, 9, 12));

    @Autowired
    private CYBKConfig config;

    /**
     * 授信金额检验
     *
     * @param amount
     * @return
     */
    public boolean validCreditAmount(BigDecimal amount) {
        return AMOUNT_LOWER_LIMIT.compareTo(amount) <= 0 && AMOUNT_HIGH_LIMIT.compareTo(amount) >= 0;
    }

    /**
     * 申请期数校验
     *
     * @param periods
     * @return
     */
    public boolean validPeriods(Integer periods) {
        return PERIODS_RANGE.contains(periods);
    }


    //    public boolean isValidTime(LocalTime actTime) {
    //        LocalTime start = LocalTime.parse(config.getCreditStartTime(), VALID_FORMAT);
    //        LocalTime end = LocalTime.parse(config.getCreditEndTime(), VALID_FORMAT);
    //        return actTime.isAfter(start) && actTime.isBefore(end);
    //    }

    /**
     * 判断年龄是否有效
     *
     * @param certNo 身份证号
     * @return 结果
     */
    public boolean isValidAge(String certNo) {
        String ageStr = certNo.substring(ID_AGE_START, ID_AGE_END);
        LocalDate birth = LocalDate.parse(ageStr, AGE_FORMAT);
        long age = AgeUtil.calcAge(birth);
        return age >= AGE_LOWER_LIMIT && age <= AGE_HIGH_LIMIT;
    }

    /**
     * 判断有效授信区域
     *
     * @param provinceCode 省代码
     * @return 结果
     */
    public boolean isValidDistrict(String provinceCode) {
        return !(provinceCode.endsWith("54")
                || provinceCode.equals("65")
                || provinceCode.equals("71")
                || provinceCode.equals("81")
                || provinceCode.equals("82"));
    }
}

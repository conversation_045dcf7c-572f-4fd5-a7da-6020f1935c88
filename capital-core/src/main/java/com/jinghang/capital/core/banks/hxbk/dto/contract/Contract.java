package com.jinghang.capital.core.banks.hxbk.dto.contract;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.math.BigDecimal;

/**
 * @作者 Mr.sandman
 * @时间 2025/07/12 11:04
 */
public class Contract {

    /**
     * 关联编号（借据编号或授信编号）
     */
    @JsonProperty("relation_no")
    private String relationNo;

    /**
     * 合同编号
     */
    @JsonProperty("contract_no")
    private String contractNo;

    /**
     * 用信合同编号
     */
    @JsonProperty("disburse_contract_no")
    private String disburseContractNo;

    /**
     * 授信合同编号
     */
    @JsonProperty("credit_contract_no")
    private String creditContractNo;

    /**
     * 合同名称
     */
    @JsonProperty("contract_name")
    private String contractName;

    /**
     * 合同类型<br>
     * 使用常量：Contract.contractType.XXX
     */
    @JsonProperty("contract_type")
    private String contractType;

    /**
     * 客户编号
     */
    @JsonProperty("custom_no")
    private String customNo;

    /**
     * 合同文件下载地址
     */
    @JsonProperty("save_path")
    private String savePath;

    /**
     * 合同金额
     */
    @JsonProperty("contract_amount")
    private BigDecimal contractAmount;

    public String getRelationNo() {
        return relationNo;
    }

    public void setRelationNo(String relationNo) {
        this.relationNo = relationNo;
    }

    public String getContractNo() {
        return contractNo;
    }

    public void setContractNo(String contractNo) {
        this.contractNo = contractNo;
    }

    public String getDisburseContractNo() {
        return disburseContractNo;
    }

    public void setDisburseContractNo(String disburseContractNo) {
        this.disburseContractNo = disburseContractNo;
    }

    public String getCreditContractNo() {
        return creditContractNo;
    }

    public void setCreditContractNo(String creditContractNo) {
        this.creditContractNo = creditContractNo;
    }

    public String getContractName() {
        return contractName;
    }

    public void setContractName(String contractName) {
        this.contractName = contractName;
    }

    public String getContractType() {
        return contractType;
    }

    public void setContractType(String contractType) {
        this.contractType = contractType;
    }

    public String getCustomNo() {
        return customNo;
    }

    public void setCustomNo(String customNo) {
        this.customNo = customNo;
    }

    public String getSavePath() {
        return savePath;
    }

    public void setSavePath(String savePath) {
        this.savePath = savePath;
    }

    public BigDecimal getContractAmount() {
        return contractAmount;
    }

    public void setContractAmount(BigDecimal contractAmount) {
        this.contractAmount = contractAmount;
    }
}

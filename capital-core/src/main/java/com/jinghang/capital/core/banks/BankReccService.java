package com.jinghang.capital.core.banks;


import com.jinghang.capital.core.entity.CYBKReconcileFile;
import com.jinghang.capital.core.service.ChannelSupport;
import com.jinghang.capital.core.vo.recc.ReccApplyVo;
import com.jinghang.capital.core.vo.recc.ReccDownloadVo;
import com.jinghang.capital.core.vo.recc.ReccFileApplyVo;
import com.jinghang.capital.core.vo.recc.ReccResultVo;

import java.util.List;

public interface BankReccService extends ChannelSupport {

    void process(ReccApplyVo apply);

    ReccResultVo query(ReccApplyVo apply);

    ReccResultVo download(ReccDownloadVo vo);

    List<CYBKReconcileFile> fileApply(ReccFileApplyVo vo);
}

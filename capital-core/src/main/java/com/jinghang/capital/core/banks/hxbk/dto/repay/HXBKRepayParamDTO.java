package com.jinghang.capital.core.banks.hxbk.dto.repay;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/07/09 10:31
 */
public class HXBKRepayParamDTO {

    /**
     * 订单号
     */
    private String orderNo;
    /**
     * 用信申请订单号
     */
    private String originalOrderNo;
    /**
     * 还款类型
     */
    private String repayType;
    /**
     * 校验还款金额
     */
    private BigDecimal validRepayAmount;
    /**
     * 银行卡号
     */
    private String bankCardNo;
    /**
     * 是否支付代扣
     */
    private String paymentFlag;
    /**
     * 平台商户号
     */
    private String merchantNo;
    /**
     * 平台交易流水号
     */
    private String platformSerialNo;
    /**
     * 账号开户名
     */
    private String accountName;
    /**
     * 银行预留手机号
     */
    private String reservedMobile;
    /**
     * 账号开户行
     */
    private String accountOpenBank;
    /**
     * 扣款渠道代码
     */
    private String deductionCode;
    /**
     * 通道签约协议号
     */
    private String signingAgreementNum;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getOriginalOrderNo() {
        return originalOrderNo;
    }

    public void setOriginalOrderNo(String originalOrderNo) {
        this.originalOrderNo = originalOrderNo;
    }

    public String getRepayType() {
        return repayType;
    }

    public void setRepayType(String repayType) {
        this.repayType = repayType;
    }

    public BigDecimal getValidRepayAmount() {
        return validRepayAmount;
    }

    public void setValidRepayAmount(BigDecimal validRepayAmount) {
        this.validRepayAmount = validRepayAmount;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public String getBankCardNo() {
        return bankCardNo;
    }

    public void setBankCardNo(String bankCardNo) {
        this.bankCardNo = bankCardNo;
    }

    public String getAccountOpenBank() {
        return accountOpenBank;
    }

    public void setAccountOpenBank(String accountOpenBank) {
        this.accountOpenBank = accountOpenBank;
    }

    public String getMerchantNo() {
        return merchantNo;
    }

    public void setMerchantNo(String merchantNo) {
        this.merchantNo = merchantNo;
    }

    public String getPaymentFlag() {
        return paymentFlag;
    }

    public void setPaymentFlag(String paymentFlag) {
        this.paymentFlag = paymentFlag;
    }

    public String getDeductionCode() {
        return deductionCode;
    }

    public void setDeductionCode(String deductionCode) {
        this.deductionCode = deductionCode;
    }

    public String getPlatformSerialNo() {
        return platformSerialNo;
    }

    public void setPlatformSerialNo(String platformSerialNo) {
        this.platformSerialNo = platformSerialNo;
    }

    public String getReservedMobile() {
        return reservedMobile;
    }

    public void setReservedMobile(String reservedMobile) {
        this.reservedMobile = reservedMobile;
    }

    public String getSigningAgreementNum() {
        return signingAgreementNum;
    }

    public void setSigningAgreementNum(String signingAgreementNum) {
        this.signingAgreementNum = signingAgreementNum;
    }

}

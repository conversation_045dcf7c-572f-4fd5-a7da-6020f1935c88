package com.jinghang.capital.core.banks.hxbk.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * 湖消蚂蚁天枢系统公共数据
 *
 * @Author: Lior
 * @CreateTime: 2025/7/7 20:17
 */
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class HXBKCommonData {
    private String baseUrl;
    private String ak;
    private String sk;

    public String getBaseUrl() {
        return baseUrl;
    }

    public void setBaseUrl(String baseUrl) {
        this.baseUrl = baseUrl;
    }

    public String getAk() {
        return ak;
    }

    public void setAk(String ak) {
        this.ak = ak;
    }

    public String getSk() {
        return sk;
    }

    public void setSk(String sk) {
        this.sk = sk;
    }
}

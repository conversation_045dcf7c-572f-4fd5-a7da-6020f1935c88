package com.jinghang.capital.core.banks;


import com.jinghang.capital.core.service.ChannelSupport;
import com.jinghang.capital.core.vo.bind.BindApplyVo;
import com.jinghang.capital.core.vo.bind.BindConfirmVo;
import com.jinghang.capital.core.vo.bind.BindResultVo;

/**
 * 绑卡服务
 */
public interface BankBindService extends ChannelSupport {

    //    //绑卡-协议同步-》信息变更
    BindResultVo apply(BindApplyVo apply);

    BindResultVo confirm(BindConfirmVo confirm);

}

package com.jinghang.capital.core.banks.hxbk.enums;


import com.jinghang.capital.core.enums.Position;
import java.util.Arrays;

/**
 *
 * 码值映射
 * 1 专业技术人员——03:学校、培训机构等教育人员，09:医生、医护等医疗人员，10:专业技术人员
 * 4 社会生产服务和生活服务人员——04:一般销售人员，如保险、房产、汽车等，05:个体经营及企业法人，06:物业、保安、保洁人员，07:一般服务人员，如KTV、酒吧、餐饮、网咖等
 * 6 生产制造及有关人员——02:一线生产、建筑、运输、配送人员
 * 7 国家机关、党群组织、企业、事业单位负责人——08:国家机关、国企央企、事业单位人员
 * X 军人——11:军人
 * Y 不便分类的其他从业——01:其他
 *
 * <AUTHOR>
 * @date 2025/7/16
 */
public enum HXBKOccupation {
    FOURTEEN("1", "学校、培训机构等教育人员", Position.FOURTEEN),
    TWENTY("1", "医生、医护等医疗人员", Position.TWENTY),
    TWENTY_ONE("1", "专业技术人员", Position.TWENTY_ONE),
    FIFTEEN("4", "一般销售人员，如保险、房产、汽车等", Position.FIFTEEN),
    SIXTEEN("4", "个体经营及企业法人", Position.SIXTEEN),
    SEVENTEEN("4", "物业、保安、保洁人员", Position.SEVENTEEN),
    EIGHTEEN("4", "一般服务人员，如KTV、酒吧、餐饮、网咖等", Position.EIGHTEEN),
    THIRTEEN("6", "一线生产、建筑、运输、配送人员", Position.THIRTEEN),
    NINETEEN("7", "国家机关、国企央企、事业单位人员", Position.NINETEEN),
    TWENTY_TWO("X", "军人", Position.TWENTY_TWO),
    ELEVEN("Y", "其他", Position.ELEVEN);
    private final String code;
    private final String desc;
    private final Position position;

    HXBKOccupation(String code, String desc, Position position) {
        this.code = code;
        this.desc = desc;
        this.position = position;
    }

    public static String getHXBKCodeByPosition(String position) {
        return Arrays.stream(values()).filter(l -> l.position.name().equals(position)).findFirst().orElse(ELEVEN).getCode();
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public Position getPosition() {
        return position;
    }
}

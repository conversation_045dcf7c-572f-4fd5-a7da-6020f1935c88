package com.jinghang.capital.core.banks.hxbk.enums;

import java.util.Arrays;

/**
 * HXBK月收入枚举
 * <p>
 * 蚂蚁月收入范围码值映射：
 * 0 - 未知
 * 1 - 5000元以下
 * 2 - 5000-10000元
 * 3 - 10001-15000元
 * 4 - 15001-20000元
 * 5 - 20001-25000元
 * 6 - 25001-30000元
 * 7 - 30000元以上
 * <p>
 * 绿信月收入范围码值映射：
 * 1 - 3000元以下
 * 2 - 3000-5000元
 * 3 - 5000-10000元
 * 4 - 10000-20000元
 * 5 - 20001元以上
 * <p>
 * income和monthSalary的对应关系：
 * 空 -> 0
 * 1和2 -> 1
 * 3 -> 2
 * 4 -> 4
 * 5 -> 5
 *
 * @Author: Lior
 * @CreateTime: 2025/7/24
 */
public enum HXBKIncome {
    UNKNOWN(null, "0", "未知"),
    INCOME_1(1, "1", "5000元以下"),
    INCOME_2(2, "1", "5000元以下"),
    INCOME_3(3, "2", "5000-10000元"),
    INCOME_4(4, "3", "10001-15000元"),
    INCOME_5(5, "5", "20001-25000元");

    private final Integer incomeCode;
    private final String monthSalaryCode;
    private final String desc;

    HXBKIncome(Integer incomeCode, String monthSalaryCode, String desc) {
        this.incomeCode = incomeCode;
        this.monthSalaryCode = monthSalaryCode;
        this.desc = desc;
    }

    /**
     * 根据收入码值获取对应的月收入码值字符串
     *
     * @param incomeCode 收入码值
     * @return 对应的月收入码值字符串，如果找不到则返回"0"
     */
    public static String convertIncomeToMonthSalary(Integer incomeCode) {
        if (incomeCode == null) {
            return UNKNOWN.monthSalaryCode;
        }

        return Arrays.stream(values())
                .filter(hxbkIncome -> incomeCode.equals(hxbkIncome.incomeCode))
                .findFirst()
                .map(HXBKIncome::getMonthSalaryCode)
                .orElse(UNKNOWN.monthSalaryCode);
    }

    /**
     * 根据收入码值获取枚举
     *
     * @param incomeCode 收入码值
     * @return HXBK收入枚举
     */
    public static HXBKIncome getEnumByIncomeCode(Integer incomeCode) {
        if (incomeCode == null) {
            return UNKNOWN;
        }

        return Arrays.stream(values())
                .filter(hxbkIncome -> incomeCode.equals(hxbkIncome.incomeCode))
                .findFirst()
                .orElse(UNKNOWN);
    }

    public Integer getIncomeCode() {
        return incomeCode;
    }

    public String getMonthSalaryCode() {
        return monthSalaryCode;
    }

    public String getDesc() {
        return desc;
    }
}

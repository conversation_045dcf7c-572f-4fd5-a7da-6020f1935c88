package com.jinghang.capital.core.banks.hxbk.dto.credit;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * HXBK联系人信息
 *
 * @Author: Lior
 * @CreateTime: 2025/7/8 10:25
 */
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class HXBKContact {

    /**
     * 联系人类型，见枚举
     * 1-配偶
     * 2-父母
     * 3-子女
     * 4-兄弟
     * 5-姐妹
     * 6-朋友
     * 7-其他
     */
    @JsonProperty("kind")
    private String kind;

    /**
     * 联系人姓名
     */
    @JsonProperty("name")
    private String name;

    /**
     * 联系人手机号
     */
    @JsonProperty("mobile")
    private String mobile;

    // Getter and Setter methods
    public String getKind() {
        return kind;
    }

    public void setKind(String kind) {
        this.kind = kind;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }
}

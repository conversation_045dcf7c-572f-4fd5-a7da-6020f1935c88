package com.jinghang.capital.core.banks.hxbk.dto.repay;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/07/10 10:31
 */
public class HXBKRepayInfoDTO {

    /**
     * 期次
     */
    @JsonProperty("period")
    private Integer period;
    /**
     * 实还总额
     */
    @JsonProperty("amount")
    private BigDecimal amount;
    /**
     * 实还本金
     */
    @JsonProperty("principal")
    private BigDecimal principal;
    /**
     * 实还利息
     */
    @JsonProperty("interest")
    private BigDecimal interest;
    /**
     * 实还通道手续费
     */
    @JsonProperty("channel_amt")
    private BigDecimal channelAmt;
    /**
     * 实还手续费
     */
    @JsonProperty("fee")
    private BigDecimal fee;
    /**
     * 实收罚息
     */
    @JsonProperty("punish")
    private BigDecimal punish;
    /**
     * 担保费
     */
    @JsonProperty("guarantee_fee")
    private BigDecimal guaranteeFee;
    /**
     * 违约金
     */
    @JsonProperty("liquidated_damages")
    private BigDecimal liquidatedDamages;
    /**
     * 服务费
     */
    @JsonProperty("service_charge")
    private BigDecimal serviceCharge;

    public Integer getPeriod() {
        return period;
    }

    public void setPeriod(Integer period) {
        this.period = period;
    }

    public BigDecimal getLiquidatedDamages() {
        return liquidatedDamages;
    }

    public void setLiquidatedDamages(BigDecimal liquidatedDamages) {
        this.liquidatedDamages = liquidatedDamages;
    }

    public BigDecimal getGuaranteeFee() {
        return guaranteeFee;
    }

    public void setGuaranteeFee(BigDecimal guaranteeFee) {
        this.guaranteeFee = guaranteeFee;
    }

    public BigDecimal getFee() {
        return fee;
    }

    public void setFee(BigDecimal fee) {
        this.fee = fee;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BigDecimal getChannelAmt() {
        return channelAmt;
    }

    public void setChannelAmt(BigDecimal channelAmt) {
        this.channelAmt = channelAmt;
    }

    public BigDecimal getInterest() {
        return interest;
    }

    public void setInterest(BigDecimal interest) {
        this.interest = interest;
    }

    public BigDecimal getPrincipal() {
        return principal;
    }

    public void setPrincipal(BigDecimal principal) {
        this.principal = principal;
    }

    public BigDecimal getPunish() {
        return punish;
    }

    public void setPunish(BigDecimal punish) {
        this.punish = punish;
    }

    public BigDecimal getServiceCharge() {
        return serviceCharge;
    }

    public void setServiceCharge(BigDecimal serviceCharge) {
        this.serviceCharge = serviceCharge;
    }

}

package com.jinghang.capital.core.banks;


import com.jinghang.capital.core.dto.QuotaAdjustApplyDto;
import com.jinghang.capital.core.dto.QuotaQueryDto;
import com.jinghang.capital.core.dto.QuotaQueryResultDto;
import com.jinghang.capital.core.entity.QuotaAdjustRecord;
import com.jinghang.capital.core.service.ChannelSupport;

public interface BankQuotaService extends ChannelSupport {

    /**
     * 额度查询
     *
     * @param dto
     */
    void quotaQuery(QuotaQueryDto dto);

    /**
     * 额度查询结果回调
     *
     * @param dto
     */
    void quotaQueryResultCallback(QuotaQueryDto dto, QuotaQueryResultDto resultDto);

    /**
     * 调额申请
     *
     * @param adjustId
     */
    void quotaAdjustApply(QuotaAdjustApplyDto quotaAdjustApplyDto, QuotaAdjustRecord record);

    /**
     * 调额申请结果查询
     *
     * @param adjustId
     */
    void quotaAdjustResultQuery(String adjustId);

}

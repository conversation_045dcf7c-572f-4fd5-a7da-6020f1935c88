package com.jinghang.capital.core.banks.hxbk.enums;

/**
 * 湖消蚂蚁天枢响应码
 *
 * @Author: Lior
 * @CreateTime: 2025/7/7 20:30
 */
public enum HXBKResponseCode {

    OK("OK", "成功"),
    FAIL("FAIL", "系统异常"),
    ORDER_NOT_EXIST("ORDER_NOT_EXIST", "订单不存在"),
    ORDER_NOT_BELONG("ORDER_NOT_BELONG", "订单不归属该用户"),
    REMOTE_ERROR("REMOTE_ERROR", "远程调用异常"),
    PARAM_ERROR("PARAM_ERROR", "参数错误"),
    PARAM_MISS("PARAM_MISS", "缺少必要参数"),
    PAYING("PAYING", "支付处理中"),
    PRODUCT_NOTEXIST("PRODUCT_NOTEXIST", "产品不存在"),
    APPLY_ERROR("APPLY_ERROR", "提交失败");

    HXBKResponseCode(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    private final String code;

    private final String msg;

    public String getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    /**
     * 判断是否成功
     *
     * @param code 响应码
     * @return true-成功，false-失败
     */
    public static boolean isOk(String code) {
        return OK.getCode().equals(code);
    }

    /**
     * 判断是否不成功
     * isNotOk
     */
    public static boolean isNotOk(String code) {
        return !isOk(code);
    }

    /**
     * 判断是否成功（别名方法）
     *
     * @param code 响应码
     * @return true-成功，false-失败
     */
    public static boolean isSuccess(String code) {
        return isOk(code);
    }

    /**
     * 判断是否失败
     *
     * @param code 响应码
     * @return true-失败，false-成功
     */
    public static boolean isFail(String code) {
        return FAIL.getCode().equals(code);
    }

    /**
     * 判断是否系统异常
     *
     * @param code 响应码
     * @return true-系统异常，false-非系统异常
     */
    public static boolean isSystemError(String code) {
        return FAIL.getCode().equals(code) || REMOTE_ERROR.getCode().equals(code);
    }

    /**
     * 判断是否参数错误
     *
     * @param code 响应码
     * @return true-参数错误，false-非参数错误
     */
    public static boolean isParamError(String code) {
        return PARAM_ERROR.getCode().equals(code) || PARAM_MISS.getCode().equals(code);
    }

    /**
     * 判断是否处理中状态
     *
     * @param code 响应码
     * @return true-处理中，false-非处理中
     */
    public static boolean isProcessing(String code) {
        return PAYING.getCode().equals(code);
    }

    /**
     * 判断是否订单相关错误
     *
     * @param code 响应码
     * @return true-订单相关错误，false-非订单相关错误
     */
    public static boolean isOrderError(String code) {
        return ORDER_NOT_EXIST.getCode().equals(code) || ORDER_NOT_BELONG.getCode().equals(code);
    }
}

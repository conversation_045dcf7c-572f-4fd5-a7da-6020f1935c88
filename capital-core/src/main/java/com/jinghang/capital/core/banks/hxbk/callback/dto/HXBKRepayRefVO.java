package com.jinghang.capital.core.banks.hxbk.callback.dto;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.math.BigDecimal;

/**
 * 还款试算-还款计划结构体
 *
 * <AUTHOR>
 * @date 2025-07-21 16:09
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class HXBKRepayRefVO {
    @JsonProperty("period")
    private Integer period;               // 期数

    @JsonProperty("amount")
    private BigDecimal amount;            // 每期应还金额 (need_amt)

    @JsonProperty("principal")
    private BigDecimal principal;         // 每期应还本金 (trans_principal)

    @JsonProperty("interest")
    private BigDecimal interest;          // 每期应还利息 (trans_interest)

    @JsonProperty("channel_amt")
    private BigDecimal channelAmt;        // 通道手续费 (无直接对应，需确认是否等同 trans_fee)

    @JsonProperty("fee")
    private BigDecimal fee;               // 还款费用 (trans_fee)

    @JsonProperty("punish")
    private BigDecimal punish;           // 罚息 (无直接对应，可能需计算)

    @JsonProperty("guarantee_fee")
    private BigDecimal guaranteeFee;      // 担保费 (guarantee_fee)

    @JsonProperty("liquidated_damages")
    private BigDecimal liquidatedDamages; // 违约金 (liquidated_damages)

    @JsonProperty("service_charge")
    private BigDecimal serviceCharge;     // 服务费 (service_charge)

    public Integer getPeriod() {
        return period;
    }

    public void setPeriod(Integer period) {
        this.period = period;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BigDecimal getPrincipal() {
        return principal;
    }

    public void setPrincipal(BigDecimal principal) {
        this.principal = principal;
    }

    public BigDecimal getInterest() {
        return interest;
    }

    public void setInterest(BigDecimal interest) {
        this.interest = interest;
    }

    public BigDecimal getChannelAmt() {
        return channelAmt;
    }

    public void setChannelAmt(BigDecimal channelAmt) {
        this.channelAmt = channelAmt;
    }

    public BigDecimal getFee() {
        return fee;
    }

    public void setFee(BigDecimal fee) {
        this.fee = fee;
    }

    public BigDecimal getPunish() {
        return punish;
    }

    public void setPunish(BigDecimal punish) {
        this.punish = punish;
    }

    public BigDecimal getGuaranteeFee() {
        return guaranteeFee;
    }

    public void setGuaranteeFee(BigDecimal guaranteeFee) {
        this.guaranteeFee = guaranteeFee;
    }

    public BigDecimal getLiquidatedDamages() {
        return liquidatedDamages;
    }

    public void setLiquidatedDamages(BigDecimal liquidatedDamages) {
        this.liquidatedDamages = liquidatedDamages;
    }

    public BigDecimal getServiceCharge() {
        return serviceCharge;
    }

    public void setServiceCharge(BigDecimal serviceCharge) {
        this.serviceCharge = serviceCharge;
    }
}


package com.jinghang.capital.core.banks.hxbk.dto.loan;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;


@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class HXBKRepayListQueryResponse {


    /**
     * 还款计划列表 ARRAY[Repay] N 成功时返回
     */
    @JsonProperty("repay_result_list")
    private List<HXBKRepayList> repayResultList;


    @JsonProperty("result_code")
    private String resultCode;

    /**
     * 结果信息 STRING Y
     */
    @JsonProperty("result_msg")
    private String resultMsg;

    /**
     * 请求唯一id STRING Y
     */
    @JsonProperty("req_msg_id")
    private String reqMsgId;


    public List<HXBKRepayList> getRepayResultList() {
        return repayResultList;
    }

    public void setRepayResultList(List<HXBKRepayList> repayResultList) {
        this.repayResultList = repayResultList;
    }

    public String getResultCode() {
        return resultCode;
    }

    public void setResultCode(String resultCode) {
        this.resultCode = resultCode;
    }

    public String getResultMsg() {
        return resultMsg;
    }

    public void setResultMsg(String resultMsg) {
        this.resultMsg = resultMsg;
    }

    public String getReqMsgId() {
        return reqMsgId;
    }

    public void setReqMsgId(String reqMsgId) {
        this.reqMsgId = reqMsgId;
    }
}

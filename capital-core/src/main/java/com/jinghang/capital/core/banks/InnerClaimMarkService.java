package com.jinghang.capital.core.banks;

import com.aliyun.oss.OSS;
import com.jinghang.capital.core.entity.BankLoanReplan;
import com.jinghang.capital.core.enums.BankChannel;
import com.jinghang.capital.core.repository.LoanRepository;
import com.jinghang.capital.core.repository.ReconciliationFileRepository;
import java.time.LocalDate;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 内部代偿标记相关服务
 */
@Service
public class InnerClaimMarkService {

    private static final Logger logger = LoggerFactory.getLogger(InnerClaimMarkService.class);

    private static final String SPLIT = "|";

    private OSS hangzhouOssClient;


    @Autowired
    private LoanRepository loanRepository;

    @Autowired
    private ReconciliationFileRepository reconFileRepository;

    public void generate360ClaimMark(LocalDate repayDate, List<BankLoanReplan> markedPlans, BankChannel channel) {
        //        try {
        //            Path tempFile = Files.createTempFile(channel.name() + "_claim_mark", ".tmp");
        //            BufferedWriter writer = Files.newBufferedWriter(tempFile);
        //
        //            markedPlans.forEach(p -> {
        //                var loan = loanRepository.findById(p.getLoanId()).orElse(null);
        //                if (null != loan) {
        //                    try {
        //                        writer.write(loan.getOuterLoanId() + SPLIT + p.getPeriod() + SPLIT + p.getRepayPurpose().name());
        //                        writer.newLine();
        //                    } catch (IOException e) {
        //                        throw new RuntimeException(e);
        //                    }
        //                }
        //            });
        //            writer.close();
        //            String fileName = channel.name() + repayDate.format(DateTimeFormatter.BASIC_ISO_DATE) + "_mark.txt";
        //            String ossPath = "fin-core/recc/claim/mark/" + fileName;
        //            String bucket = config.getQinjiaOssBucket();
        //            hangzhouOssClient.putObject(bucket, ossPath, Files.newInputStream(tempFile));
        //
        //            ReconciliationFile recFile = new ReconciliationFile();
        //            recFile.setFileName(fileName);
        //            recFile.setFileDate(repayDate);
        //            recFile.setReconciliationState(ReccStateEnum.S);
        //            recFile.setTargetOssBucket(bucket);
        //            recFile.setTargetOssKey(ossPath);
        //            recFile.setFileType(FileType.COMPENSATION_INNER_MARK_FILE);
        //            recFile.setSyncState(SyncState.Y);
        //            recFile.setMode(FileMode.OSS);
        //            recFile.setProduct(ProductVo.ZC_360);
        //            recFile.setBankChannel(channel);
        //            reconFileRepository.save(recFile);
        //
        //        } catch (IOException e) {
        //            logger.error("create claim mark temp file error.", e);
        //
        //        }


    }

    @Autowired
    public void setHangzhouOssClient(OSS hangzhouOssClient) {
        this.hangzhouOssClient = hangzhouOssClient;
    }
}

package com.jinghang.capital.core.banks;

import com.jinghang.capital.api.dto.QuotaStage;
import com.jinghang.capital.core.dto.QuotaQueryDto;
import com.jinghang.capital.core.dto.QuotaQueryResultDto;
import com.jinghang.capital.core.entity.Credit;
import com.jinghang.capital.core.entity.Loan;
import com.jinghang.capital.core.entity.QuotaAdjustRecord;
import com.jinghang.capital.core.entity.QuotaCycleUserInfo;
import com.jinghang.capital.core.enums.BankChannel;
import com.jinghang.capital.core.enums.ProcessStatus;
import com.jinghang.capital.core.repository.QuotaAdjustRecordRepository;
import com.jinghang.capital.core.repository.QuotaCycleUserInfoRepository;
import com.jinghang.capital.core.service.CommonService;
import com.jinghang.capital.core.service.MqService;
import com.jinghang.capital.core.service.WarningService;
import com.jinghang.capital.core.service.event.QuotaQueryResultEvent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;

public abstract class AbstractBankQuotaService implements BankQuotaService {
    private static final Logger logger = LoggerFactory.getLogger(AbstractBankQuotaService.class);

    @Autowired
    private ApplicationEventPublisher eventPublisher;

    @Autowired
    private CommonService commonService;

    @Autowired
    private WarningService warningService;

    @Autowired
    private MqService mqService;

    @Autowired
    private QuotaCycleUserInfoRepository quotaCycleUserInfoRepository;

    @Autowired
    private QuotaAdjustRecordRepository quotaAdjustRecordRepository;


    @Override
    public void quotaQuery(QuotaQueryDto dto) {
        QuotaCycleUserInfo userInfo = getQuotaCycleUserInfo(dto.getStage(), dto.getBusinessId());
        QuotaQueryResultDto resultDto = queryBankQuota(userInfo);
        eventPublisher.publishEvent(new QuotaQueryResultEvent(dto, resultDto));
    }

    @Override
    public void quotaQueryResultCallback(QuotaQueryDto dto, QuotaQueryResultDto resultDto) {
    }

    /**
     * 检查是否存在授信处理中
     *
     * @param channel    资方
     * @param userIdCard 用户身份证号码
     * @return 存在 true 不存在 false
     */
    public boolean checkQuotaAdjustProcessing(BankChannel channel, String userIdCard) {
        return quotaAdjustRecordRepository.existsByChannelAndUserIdCardAndStatus(channel, userIdCard, ProcessStatus.PROCESSING);
    }

    /**
     * 获取循环额度用户信息
     *
     * @param stage
     * @param businessId
     * @return
     */
    protected QuotaCycleUserInfo getQuotaCycleUserInfo(QuotaStage stage, String businessId) {
        if (QuotaStage.CREDIT.equals(stage)) {
            // 授信阶段
            Credit credit = commonService.findCreditById(businessId);
            return quotaCycleUserInfoRepository.findByChannelAndUserIdCard(
                    credit.getChannel(), credit.getCustCertNo()).orElseThrow();
        } else if (QuotaStage.LOAN.equals(stage)) {
            // 放款阶段
            Loan loan = commonService.findLoanById(businessId);
            return quotaCycleUserInfoRepository.findByChannelAndUserIdCard(
                    loan.getChannel(), loan.getCustCertNo()).orElseThrow();
        }
        // 调额阶段
        QuotaAdjustRecord quotaAdjustRecord = commonService.findQuotaAdjustRecordById(businessId).orElseThrow();
        return quotaCycleUserInfoRepository.findByChannelAndUserIdCard(
                quotaAdjustRecord.getChannel(), quotaAdjustRecord.getUserIdCard()).orElseThrow();
    }

    /**
     * 请求资方额度查询
     *
     * @param quotaCycleUserInfo
     */
    protected abstract QuotaQueryResultDto queryBankQuota(QuotaCycleUserInfo quotaCycleUserInfo);

    protected CommonService getCommonService() {
        return commonService;
    }

    protected WarningService getWarningService() {
        return warningService;
    }

    protected MqService getMqService() {
        return mqService;
    }

    protected QuotaCycleUserInfoRepository getQuotaCycleUserInfoRepository() {
        return quotaCycleUserInfoRepository;
    }

    protected QuotaAdjustRecordRepository getQuotaAdjustRecordRepository() {
        return quotaAdjustRecordRepository;
    }
}

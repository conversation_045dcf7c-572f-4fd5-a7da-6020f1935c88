package com.jinghang.capital.core.banks.hxbk.dto.credit;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * HXBK工作信息
 *
 * @Author: Lior
 * @CreateTime: 2025/7/8 10:10
 */
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class HXBKJobInfo {

    /**
     * 职业
     */
    @JsonProperty("job_type")
    private String jobType;

    /**
     * 职务
     */
    @JsonProperty("work_position")
    private String workPosition;

    /**
     * 工作年限
     */
    @JsonProperty("job_life")
    private Integer jobLife;

    /**
     * 本单位工作年限
     */
    @JsonProperty("current_job_life")
    private Integer currentJobLife;

    /**
     * 公司名称
     */
    @JsonProperty("company_name")
    private String companyName;

    /**
     * 公司行业类型
     * IN001 农、林、牧、渔业
     * IN002 采掘业
     * IN003 制造业
     * IN004 电力、燃气及水的生产和供应业
     * IN005 建筑业
     * IN006 交通运输、仓储和邮政业
     * IN007 信息传输、计算机服务和软件业
     * IN008 批发和零售业
     * IN009 住宿和餐饮业
     * IN010 金融业
     * IN011 房地产业
     * IN012 租赁和商务服务业
     * IN013 科学研究、技术服务业和地质勘察业
     * IN014 水利、环境和公共设施管理业
     * IN015 居民服务和其他服务业
     * IN016 教育
     * IN017 卫生、社会保障和社会福利业
     * IN018 文化、体育和娱乐业
     * IN019 公共管理和社会组织
     * IN020 国际组织
     * IN021 未知
     */
    @JsonProperty("company_industry_type")
    private String companyIndustryType;

    /**
     * 公司电话
     */
    @JsonProperty("company_tel")
    private String companyTel;

    /**
     * 公司省份
     */
    @JsonProperty("company_province")
    private String companyProvince;

    /**
     * 公司城市
     */
    @JsonProperty("company_city")
    private String companyCity;

    /**
     * 公司区域
     */
    @JsonProperty("company_area")
    private String companyArea;

    /**
     * 公司省份编码
     * 按国标6位码值传输
     */
    @JsonProperty("company_province_code")
    private String companyProvinceCode;

    /**
     * 公司城市编码
     * 按国标6位码值传输
     */
    @JsonProperty("company_city_code")
    private String companyCityCode;

    /**
     * 公司区域编码
     * 按国标6位码值传输
     */
    @JsonProperty("company_area_code")
    private String companyAreaCode;

    /**
     * 公司街道
     */
    @JsonProperty("company_street")
    private String companyStreet;

    /**
     * 公司详细地址
     */
    @JsonProperty("company_address")
    private String companyAddress;

    /**
     * 年收入
     */
    @JsonProperty("year_salary")
    private Long yearSalary;

    // Getter and Setter methods
    public String getJobType() {
        return jobType;
    }

    public void setJobType(String jobType) {
        this.jobType = jobType;
    }

    public String getWorkPosition() {
        return workPosition;
    }

    public void setWorkPosition(String workPosition) {
        this.workPosition = workPosition;
    }

    public Integer getJobLife() {
        return jobLife;
    }

    public void setJobLife(Integer jobLife) {
        this.jobLife = jobLife;
    }

    public Integer getCurrentJobLife() {
        return currentJobLife;
    }

    public void setCurrentJobLife(Integer currentJobLife) {
        this.currentJobLife = currentJobLife;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getCompanyIndustryType() {
        return companyIndustryType;
    }

    public void setCompanyIndustryType(String companyIndustryType) {
        this.companyIndustryType = companyIndustryType;
    }

    public String getCompanyTel() {
        return companyTel;
    }

    public void setCompanyTel(String companyTel) {
        this.companyTel = companyTel;
    }

    public String getCompanyProvince() {
        return companyProvince;
    }

    public void setCompanyProvince(String companyProvince) {
        this.companyProvince = companyProvince;
    }

    public String getCompanyCity() {
        return companyCity;
    }

    public void setCompanyCity(String companyCity) {
        this.companyCity = companyCity;
    }

    public String getCompanyArea() {
        return companyArea;
    }

    public void setCompanyArea(String companyArea) {
        this.companyArea = companyArea;
    }

    public String getCompanyProvinceCode() {
        return companyProvinceCode;
    }

    public void setCompanyProvinceCode(String companyProvinceCode) {
        this.companyProvinceCode = companyProvinceCode;
    }

    public String getCompanyCityCode() {
        return companyCityCode;
    }

    public void setCompanyCityCode(String companyCityCode) {
        this.companyCityCode = companyCityCode;
    }

    public String getCompanyAreaCode() {
        return companyAreaCode;
    }

    public void setCompanyAreaCode(String companyAreaCode) {
        this.companyAreaCode = companyAreaCode;
    }

    public String getCompanyStreet() {
        return companyStreet;
    }

    public void setCompanyStreet(String companyStreet) {
        this.companyStreet = companyStreet;
    }

    public String getCompanyAddress() {
        return companyAddress;
    }

    public void setCompanyAddress(String companyAddress) {
        this.companyAddress = companyAddress;
    }

    public Long getYearSalary() {
        return yearSalary;
    }

    public void setYearSalary(Long yearSalary) {
        this.yearSalary = yearSalary;
    }
}

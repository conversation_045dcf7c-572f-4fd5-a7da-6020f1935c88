package com.jinghang.capital.core.banks.hxbk.dto.credit;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * HXBK天枢系统授信申请响应
 *
 * @Author: Lior
 * @CreateTime: 2025/7/8 10:35
 */
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class HXBKCreditApplyResponse {

    /**
     * 客户编号
     */
    @JsonProperty("custom_no")
    private String customNo;

    /**
     * 结果code
     * 详见附录
     */
    @JsonProperty("result_code")
    private String resultCode;

    /**
     * 结果信息
     */
    @JsonProperty("result_msg")
    private String resultMsg;

    /**
     * 请求唯一id
     */
    @JsonProperty("req_msg_id")
    private String reqMsgId;

    // Getter and Setter methods
    public String getCustomNo() {
        return customNo;
    }

    public void setCustomNo(String customNo) {
        this.customNo = customNo;
    }

    public String getResultCode() {
        return resultCode;
    }

    public void setResultCode(String resultCode) {
        this.resultCode = resultCode;
    }

    public String getResultMsg() {
        return resultMsg;
    }

    public void setResultMsg(String resultMsg) {
        this.resultMsg = resultMsg;
    }

    public String getReqMsgId() {
        return reqMsgId;
    }

    public void setReqMsgId(String reqMsgId) {
        this.reqMsgId = reqMsgId;
    }
}

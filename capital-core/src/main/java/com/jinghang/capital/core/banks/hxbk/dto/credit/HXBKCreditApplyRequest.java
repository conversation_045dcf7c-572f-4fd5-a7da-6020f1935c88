package com.jinghang.capital.core.banks.hxbk.dto.credit;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.jinghang.capital.core.banks.hxbk.dto.HXBKBaseRequest;
import com.jinghang.capital.core.banks.hxbk.enums.HXBKMethod;

/**
 * HXBK天枢系统授信申请请求
 *
 * @Author: Lior
 * @CreateTime: 2025/7/8 10:00
 */
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class HXBKCreditApplyRequest extends HXBKBaseRequest {

    @JsonIgnore
    private static final HXBKMethod METHOD = HXBKMethod.CREDIT_APPLY;

    /**
     * 订单号
     * 必填
     */
    @JsonProperty("order_no")
    private String orderNo;

    /**
     * 渠道类型
     * 1: APP, 2: 支付宝小程序, 3：微信小程序, 4：微信公众号, 5：PC网页
     * 默认1
     */
    @JsonProperty("channel_type")
    private String channelType;

    /**
     * 客户类型
     * 1: 新增客户, 2：存量激活客户, 3：目标营销客户
     * 默认1
     */
    @JsonProperty("custom_type")
    private String customType;

    /**
     * 资金方代码
     */
    @JsonProperty("fund_code")
    private String fundCode;

    /**
     * 资产方用户唯一标识
     * 必填
     */
    @JsonProperty("open_id")
    private String openId;

    /**
     * 基础信息
     * 必填
     */
    @JsonProperty("personal_info")
    private HXBKPersonalInfo personalInfo;

    /**
     * 居住信息
     */
    @JsonProperty("borrower_live_info")
    private HXBKLiveInfo borrowerLiveInfo;

    /**
     * 工作信息
     * 必填
     */
    @JsonProperty("borrower_emp_info")
    private HXBKJobInfo borrowerEmpInfo;

    /**
     * 产品编号
     */
    @JsonProperty("prod_no")
    private String prodNo;

    /**
     * 风险数据对象 json字符串
     */
    @JsonProperty("risk_data")
    private String riskData;

    /**
     * 扩展字段 json字符串(信贷系统会插入saasTenantToken字段)
     */
    @JsonProperty("ext_info")
    private String extInfo;

    /**
     * 借款原因
     */
    @JsonProperty("loan_reason")
    private String loanReason;

    /**
     * 导流平台
     */
    @JsonProperty("traffic_platform")
    private String trafficPlatform;

    /**
     * 流量来源名称
     */
    @JsonProperty("traffic_source_name")
    private String trafficSourceName;

    /**
     * 广告位id
     */
    @JsonProperty("traffic_ad_id")
    private String trafficAdId;

    /**
     * 营销活动编号
     */
    @JsonProperty("traffic_mkt_id")
    private String trafficMktId;

    /**
     * 点击id
     */
    @JsonProperty("click_id")
    private String clickId;

    /**
     * 资料文件
     */
    @JsonProperty("materials")
    private HXBKMaterial[] materials;

    /**
     * 申请金额
     */
    @JsonProperty("applications_amount")
    private String applicationsAmount;

    /**
     * 申请期限（月）
     */
    @JsonProperty("application_deadline")
    private String applicationDeadline;

    /**
     * 发证机关名称
     */
    @JsonProperty("authority_name")
    private String authorityName;

    /**
     * 月收入
     */
    @JsonProperty("month_salary")
    private String monthSalary;

    @Override
    public HXBKMethod getMethod() {
        return METHOD;
    }

    // Getter and Setter methods
    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getChannelType() {
        return channelType;
    }

    public void setChannelType(String channelType) {
        this.channelType = channelType;
    }

    public String getCustomType() {
        return customType;
    }

    public void setCustomType(String customType) {
        this.customType = customType;
    }

    public String getFundCode() {
        return fundCode;
    }

    public void setFundCode(String fundCode) {
        this.fundCode = fundCode;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public HXBKPersonalInfo getPersonalInfo() {
        return personalInfo;
    }

    public void setPersonalInfo(HXBKPersonalInfo personalInfo) {
        this.personalInfo = personalInfo;
    }

    public HXBKLiveInfo getBorrowerLiveInfo() {
        return borrowerLiveInfo;
    }

    public void setBorrowerLiveInfo(HXBKLiveInfo borrowerLiveInfo) {
        this.borrowerLiveInfo = borrowerLiveInfo;
    }

    public HXBKJobInfo getBorrowerEmpInfo() {
        return borrowerEmpInfo;
    }

    public void setBorrowerEmpInfo(HXBKJobInfo borrowerEmpInfo) {
        this.borrowerEmpInfo = borrowerEmpInfo;
    }

    public String getProdNo() {
        return prodNo;
    }

    public void setProdNo(String prodNo) {
        this.prodNo = prodNo;
    }

    public String getRiskData() {
        return riskData;
    }

    public void setRiskData(String riskData) {
        this.riskData = riskData;
    }

    public String getExtInfo() {
        return extInfo;
    }

    public void setExtInfo(String extInfo) {
        this.extInfo = extInfo;
    }

    public String getLoanReason() {
        return loanReason;
    }

    public void setLoanReason(String loanReason) {
        this.loanReason = loanReason;
    }

    public String getTrafficPlatform() {
        return trafficPlatform;
    }

    public void setTrafficPlatform(String trafficPlatform) {
        this.trafficPlatform = trafficPlatform;
    }

    public String getTrafficSourceName() {
        return trafficSourceName;
    }

    public void setTrafficSourceName(String trafficSourceName) {
        this.trafficSourceName = trafficSourceName;
    }

    public String getTrafficAdId() {
        return trafficAdId;
    }

    public void setTrafficAdId(String trafficAdId) {
        this.trafficAdId = trafficAdId;
    }

    public String getTrafficMktId() {
        return trafficMktId;
    }

    public void setTrafficMktId(String trafficMktId) {
        this.trafficMktId = trafficMktId;
    }

    public String getClickId() {
        return clickId;
    }

    public void setClickId(String clickId) {
        this.clickId = clickId;
    }

    public HXBKMaterial[] getMaterials() {
        return materials;
    }

    public void setMaterials(HXBKMaterial[] materials) {
        this.materials = materials;
    }

    public String getApplicationsAmount() {
        return applicationsAmount;
    }

    public void setApplicationsAmount(String applicationsAmount) {
        this.applicationsAmount = applicationsAmount;
    }

    public String getApplicationDeadline() {
        return applicationDeadline;
    }

    public void setApplicationDeadline(String applicationDeadline) {
        this.applicationDeadline = applicationDeadline;
    }

    public String getAuthorityName() {
        return authorityName;
    }

    public void setAuthorityName(String authorityName) {
        this.authorityName = authorityName;
    }

    public String getMonthSalary() {
        return monthSalary;
    }

    public void setMonthSalary(String monthSalary) {
        this.monthSalary = monthSalary;
    }
}

package com.jinghang.capital.core.banks.hxbk.callback.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * 湖消回调响应公共参数
 *
 * @Author: Lior
 * @CreateTime: 2025/7/8 19:58
 */
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class HXBKCallbackResponse {

    /**
     * 响应码
     * 如:000000 成功,详情请参考响应码描述
     */
    private String code;

    /**
     * 描述
     * 对应的描述信息
     */
    private String msg;

    /**
     * 业务参数
     * 具体业务接口的响应参数（code=000000时有值）
     */
    private String data;

    /**
     * 数字签名
     * 响应方生成的数字签名字符串
     */
    private String sign;

    /**
     * 加密标识
     * 标识业务报文是否加密，只有1.默认1
     */
    private String encrypt;

    /**
     * 加密秘钥
     * 对称秘钥密文，encrypt=1时必传
     */
    private String secretKey;

    /**
     * 响应ID
     * 标识一次响应，保证唯一
     */
    private String responseId;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public String getSign() {
        return sign;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }

    public String getEncrypt() {
        return encrypt;
    }

    public void setEncrypt(String encrypt) {
        this.encrypt = encrypt;
    }

    public String getSecretKey() {
        return secretKey;
    }

    public void setSecretKey(String secretKey) {
        this.secretKey = secretKey;
    }

    public String getResponseId() {
        return responseId;
    }

    public void setResponseId(String responseId) {
        this.responseId = responseId;
    }
}

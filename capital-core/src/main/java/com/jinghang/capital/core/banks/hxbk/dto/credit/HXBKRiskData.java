package com.jinghang.capital.core.banks.hxbk.dto.credit;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;

/**
 * HXBK风险数据对象
 * 根据风控需求扩展
 *
 * @Author: Lior
 * @CreateTime: 2025/7/8 10:30
 */
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class HXBKRiskData {

    /**
     * 联系人信息列表
     */
    @JsonProperty("contacts")
    private List<HXBKContact> hxbkContacts;

    /**
     * 借据金额
     * 借据提现金额，单位为分。1500000表示提现金额为15000元
     */
    @JsonProperty("loan_amount")
    private Long loanAmount;

    /**
     * 提现利率
     * 授信年利率，精确到小数点后4位0.2388，表示年利率为23.88%
     */
    @JsonProperty("credit_rate")
    private Double creditRate;

    /**
     * 已授信有效平均额度
     * 传入存量客户的当前有效授信总额度(多个机构当前有效授信额度平均)
     */
    @JsonProperty("credited_amt_avg")
    private Long creditedAmtAvg;

    /**
     * 已授信平均利率
     * 传入存量客户的当前利率(多个机构取平均，排除利率为空的异常情况)
     */
    @JsonProperty("credited_interest_rate_avg")
    private Double creditedInterestRateAvg;

    /**
     * 已授信最低利率
     * 传入存量客户的当前利率(多个机构取最小值，且排除利率为空异常情况)
     */
    @JsonProperty("credited_interest_rate_min")
    private Double creditedInterestRateMin;

    /**
     * 用户授信分类
     * unloan: 授信未支用用户
     * withdrawing: 待清退机构授信用户
     */
    @JsonProperty("cust_loan_type")
    private String custLoanType;

    /**
     * 2088id
     */
    @JsonProperty("alipay_id")
    private String alipayId;

    // Getter and Setter methods
    public List<HXBKContact> getContacts() {
        return hxbkContacts;
    }

    public void setContacts(List<HXBKContact> hxbkContacts) {
        this.hxbkContacts = hxbkContacts;
    }

    public Long getLoanAmount() {
        return loanAmount;
    }

    public void setLoanAmount(Long loanAmount) {
        this.loanAmount = loanAmount;
    }

    public Double getCreditRate() {
        return creditRate;
    }

    public void setCreditRate(Double creditRate) {
        this.creditRate = creditRate;
    }

    public Long getCreditedAmtAvg() {
        return creditedAmtAvg;
    }

    public void setCreditedAmtAvg(Long creditedAmtAvg) {
        this.creditedAmtAvg = creditedAmtAvg;
    }

    public Double getCreditedInterestRateAvg() {
        return creditedInterestRateAvg;
    }

    public void setCreditedInterestRateAvg(Double creditedInterestRateAvg) {
        this.creditedInterestRateAvg = creditedInterestRateAvg;
    }

    public Double getCreditedInterestRateMin() {
        return creditedInterestRateMin;
    }

    public void setCreditedInterestRateMin(Double creditedInterestRateMin) {
        this.creditedInterestRateMin = creditedInterestRateMin;
    }

    public String getCustLoanType() {
        return custLoanType;
    }

    public void setCustLoanType(String custLoanType) {
        this.custLoanType = custLoanType;
    }

    public String getAlipayId() {
        return alipayId;
    }

    public void setAlipayId(String alipayId) {
        this.alipayId = alipayId;
    }
}

package com.jinghang.capital.core.banks.cybk.convert;


import com.jinghang.capital.core.banks.cybk.dto.credit.CYBKCreditApplyRequest;
import com.jinghang.capital.core.banks.cybk.dto.credit.CYBKCreditApplyResponse;
import com.jinghang.capital.core.banks.cybk.dto.credit.CYBKCreditQueryResponse;
import com.jinghang.capital.core.banks.cybk.dto.credit.CYBKPreCreditResponse;
import com.jinghang.capital.core.banks.cybk.dto.limit.CYBKLimitQueryResponse;
import com.jinghang.capital.core.dto.QuotaQueryResultDto;
import com.jinghang.capital.core.entity.Credit;
import com.jinghang.capital.core.enums.ProcessStatus;
import com.jinghang.capital.core.vo.credit.CreditResultVo;
import com.jinghang.capital.core.vo.credit.PreCreditApplyResultVo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @date 2023/8/20
 */
@Mapper(uses = {CYBKEnumConvert.class})
public interface CYBKCreditConvert {
    CYBKCreditConvert INSTANCE = Mappers.getMapper(CYBKCreditConvert.class);

    CYBKCreditApplyRequest toReq(Credit credit);

    @Mapping(source = "applCde", target = "creditNo")
    CreditResultVo toVo(CYBKCreditApplyResponse response);

    @Mapping(source = "contractNo", target = "creditContractNo")
    @Mapping(source = "baseLimit", target = "creditResultAmt")
    @Mapping(source = "outRiskMsg", target = "failMsg")
    @Mapping(source = "outApplSeq", target = "creditSeq")
    @Mapping(source = "outSts", target = "status", qualifiedByName = "toProcessStatus")
    CreditResultVo toVo(CYBKCreditQueryResponse response);

    @Mapping(source = "result", target = "ruleCode")
    PreCreditApplyResultVo toVo(CYBKPreCreditResponse response);


    @Mapping(source = "limitStatus", target = "quotaStatus")
    @Mapping(source = "limitAmt", target = "creditQuota")
    @Mapping(source = "usedLimit", target = "usedQuota")
    @Mapping(source = "balanceLimit", target = "balanceQuota")
    QuotaQueryResultDto toVo(CYBKLimitQueryResponse response);

    @Named("toProcessStatus")
    default ProcessStatus toProcessStatus(String cybkStatus) {
        return switch (cybkStatus) {
            case "01" -> ProcessStatus.PROCESSING;
            case "03" -> ProcessStatus.SUCCESS;
            case "02" -> ProcessStatus.FAIL;
            default -> throw new IllegalArgumentException("Unexpected response status:" + cybkStatus);
        };
    }
}

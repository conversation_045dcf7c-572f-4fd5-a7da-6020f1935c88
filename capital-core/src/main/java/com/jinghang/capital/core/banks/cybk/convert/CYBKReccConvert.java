package com.jinghang.capital.core.banks.cybk.convert;

import com.jinghang.capital.core.banks.cybk.recc.dto.CYBKCustDailyLoan;
import com.jinghang.capital.core.banks.cybk.recc.dto.CYBKCustDailyLoanPlan;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @date 2023/8/20
 */
@Mapper(uses = {CYBKEnumConvert.class})
public interface CYBKReccConvert {
    CYBKReccConvert INSTANCE = Mappers.getMapper(CYBKReccConvert.class);

    @Mapping(ignore = true, target = "status")
    @Mapping(ignore = true, target = "clearDate")
    @Mapping(source = "endDate", target = "nextRepayDate")
    CYBKCustDailyLoan toDailyLoan(CYBKCustDailyLoanPlan plan);
}

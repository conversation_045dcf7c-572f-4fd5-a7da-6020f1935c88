package com.jinghang.capital.core.banks.hxbk.dto.loan;

public class BasicInfoReq {
    /**
     * 申请人证件号
     */
    private String idNo;
    /**
     * 证件有效期起止
     */
    private String idNoStartDate;
    /**
     * 申请人证件号
     */
    private String idNoEndDate;
    /**
     * 身份证发证机构
     */
    private String idOrgan;
    /**
     * 出生日期
     */
    private String bornDate;
    /**
     * 手机号
     */
    private String indivMobile;
    /**
     * 出生日期
     */
    private String indivMarital;
    /**
     * 最高学历
     */
    private String indivEdu;
    /**
     * 最高学位
     */
    private String indivDegree;
    /**
     * 毕业学校
     */
    private String graduateSchoolName;

    public String getIdNo() {
        return idNo;
    }

    public void setIdNo(String idNo) {
        this.idNo = idNo;
    }

    public String getIdNoStartDate() {
        return idNoStartDate;
    }

    public void setIdNoStartDate(String idNoStartDate) {
        this.idNoStartDate = idNoStartDate;
    }

    public String getIdNoEndDate() {
        return idNoEndDate;
    }

    public void setIdNoEndDate(String idNoEndDate) {
        this.idNoEndDate = idNoEndDate;
    }

    public String getIdOrgan() {
        return idOrgan;
    }

    public void setIdOrgan(String idOrgan) {
        this.idOrgan = idOrgan;
    }

    public String getBornDate() {
        return bornDate;
    }

    public void setBornDate(String bornDate) {
        this.bornDate = bornDate;
    }

    public String getIndivMobile() {
        return indivMobile;
    }

    public void setIndivMobile(String indivMobile) {
        this.indivMobile = indivMobile;
    }

    public String getIndivMarital() {
        return indivMarital;
    }

    public void setIndivMarital(String indivMarital) {
        this.indivMarital = indivMarital;
    }

    public String getIndivEdu() {
        return indivEdu;
    }

    public void setIndivEdu(String indivEdu) {
        this.indivEdu = indivEdu;
    }

    public String getIndivDegree() {
        return indivDegree;
    }

    public void setIndivDegree(String indivDegree) {
        this.indivDegree = indivDegree;
    }

    public String getGraduateSchoolName() {
        return graduateSchoolName;
    }

    public void setGraduateSchoolName(String graduateSchoolName) {
        this.graduateSchoolName = graduateSchoolName;
    }
}

package com.jinghang.capital.core.banks.hxbk.dto.repay;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.jinghang.capital.core.banks.hxbk.dto.HXBKBaseRequest;
import com.jinghang.capital.core.banks.hxbk.enums.HXBKMethod;
import java.math.BigDecimal;

@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class HXBKRepayApplyRequest extends HXBKBaseRequest {
    @JsonIgnore
    private static final HXBKMethod TRADE_CODE = HXBKMethod.REPAY_APPLY;

    /**
     * 订单号
     */
    @JsonProperty("order_no")
    private String orderNo;
    /**
     * 用信申请订单号
     */
    @JsonProperty("original_order_no")
    private String originalOrderNo;
    /**
     * 还款类型
     */
    @JsonProperty("repay_type")
    private String repayType;
    /**
     * 校验还款金额
     */
    @JsonProperty("valid_repay_amount")
    private BigDecimal validRepayAmount;
    /**
     * 银行卡号
     */
    @JsonProperty("bank_card_no")
    private String bankCardNo;
    /**
     * 是否支付代扣
     */
    @JsonProperty("payment_flag")
    private String paymentFlag;
    /**
     * 平台商户号
     */
    @JsonProperty("merchant_no")
    private String merchantNo;
    /**
     * 平台交易流水号
     */
    @JsonProperty("platform_serial_no")
    private String platformSerialNo;
    /**
     * 账号开户名
     */
    @JsonProperty("account_name")
    private String accountName;
    /**
     * 银行预留手机号
     */
    @JsonProperty("reserved_mobile")
    private String reservedMobile;
    /**
     * 账号开户行
     */
    @JsonProperty("account_open_bank")
    private String accountOpenBank;
    /**
     * 扣款渠道代码
     */
    @JsonProperty("deduction_code")
    private String deductionCode;
    /**
     * 通道签约协议号
     */
    @JsonProperty("signing_agreement_num")
    private String signingAgreementNum;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getOriginalOrderNo() {
        return originalOrderNo;
    }

    public void setOriginalOrderNo(String originalOrderNo) {
        this.originalOrderNo = originalOrderNo;
    }

    public String getRepayType() {
        return repayType;
    }

    public void setRepayType(String repayType) {
        this.repayType = repayType;
    }

    public BigDecimal getValidRepayAmount() {
        return validRepayAmount;
    }

    public void setValidRepayAmount(BigDecimal validRepayAmount) {
        this.validRepayAmount = validRepayAmount;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public String getBankCardNo() {
        return bankCardNo;
    }

    public void setBankCardNo(String bankCardNo) {
        this.bankCardNo = bankCardNo;
    }

    public String getAccountOpenBank() {
        return accountOpenBank;
    }

    public void setAccountOpenBank(String accountOpenBank) {
        this.accountOpenBank = accountOpenBank;
    }

    public String getMerchantNo() {
        return merchantNo;
    }

    public void setMerchantNo(String merchantNo) {
        this.merchantNo = merchantNo;
    }

    public String getPaymentFlag() {
        return paymentFlag;
    }

    public void setPaymentFlag(String paymentFlag) {
        this.paymentFlag = paymentFlag;
    }

    public String getDeductionCode() {
        return deductionCode;
    }

    public void setDeductionCode(String deductionCode) {
        this.deductionCode = deductionCode;
    }

    public String getPlatformSerialNo() {
        return platformSerialNo;
    }

    public void setPlatformSerialNo(String platformSerialNo) {
        this.platformSerialNo = platformSerialNo;
    }

    public String getReservedMobile() {
        return reservedMobile;
    }

    public void setReservedMobile(String reservedMobile) {
        this.reservedMobile = reservedMobile;
    }

    public String getSigningAgreementNum() {
        return signingAgreementNum;
    }

    public void setSigningAgreementNum(String signingAgreementNum) {
        this.signingAgreementNum = signingAgreementNum;
    }

    @Override
    public HXBKMethod getMethod() {
        return TRADE_CODE;
    }

}

package com.jinghang.capital.core.banks;


import com.jinghang.capital.core.convert.entityvo.VoCreditConvert;
import com.jinghang.capital.core.dto.AdjustLimitBizDto;
import com.jinghang.capital.core.dto.QuotaQueryDto;
import com.jinghang.capital.core.dto.QuotaQueryResultDto;
import com.jinghang.capital.core.entity.Credit;
import com.jinghang.capital.core.entity.FailedCredit;
import com.jinghang.capital.core.enums.CreditStatus;
import com.jinghang.capital.core.enums.ProcessStatus;
import com.jinghang.capital.core.exception.BizErrorCode;
import com.jinghang.capital.core.exception.BizException;
import com.jinghang.capital.core.service.CommonService;
import com.jinghang.capital.core.service.MqService;
import com.jinghang.capital.core.service.credit.FinCreditService;
import com.jinghang.capital.core.util.AgeUtil;
import com.jinghang.capital.core.vo.credit.CreditApplyVo;
import com.jinghang.capital.core.vo.credit.CreditQueryVo;
import com.jinghang.capital.core.vo.credit.CreditResultVo;
import com.jinghang.capital.core.vo.credit.ExtInfoVo;
import com.jinghang.capital.core.vo.credit.PreCreditApplyResultVo;
import com.jinghang.capital.core.vo.credit.PreCreditApplyVo;
import com.jinghang.capital.core.vo.credit.RecreditApplyVo;
import com.jinghang.common.util.DateUtil;
import com.jinghang.common.util.StringUtil;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

public abstract class AbstractBankCreditService implements BankCreditService {

    @Autowired
    private CommonService commonService;

    @Autowired
    private FinCreditService finCreditService;

    @Autowired
    private MqService mqService;

    private static final BigDecimal DEFAULT_BANK_RATE = new BigDecimal("0.075");

    // 时分时间格式
    protected static final DateTimeFormatter FORMATTER_HH_MM = DateTimeFormatter.ofPattern("HHmm");

    // 身份证上出生日期索引开始
    private static final int BIRTHDAY_INDEX_START = 6;

    // 身份证上出生日期索引结束
    private static final int BIRTHDAY_INDEX_END = 14;

    @Override
    public CreditResultVo apply(CreditApplyVo<ExtInfoVo> apply) {


        // 30天内授信失败检验
        List<Credit> creditList = finCreditService.queryThirtyDayCreditFailRecord(apply.getBankChannel(), apply.getIdCardInfo().getCertNo());
        if (!CollectionUtils.isEmpty(creditList)) {
            throw new BizException(BizErrorCode.CREDIT_FAIL_IN_THIRTY_DAY);
        }
        // 授信记录
        Credit credit = bankCreditValidate(apply);
        if (Objects.isNull(credit)) {
            credit = commonService.creditApply(apply);
            // 设置新客或者老客
            credit.setProductType(getProductType());
            credit = commonService.saveCredit(credit);
        }

        if (CreditStatus.SUCCESS.equals(credit.getCreditStatus()) || CreditStatus.FAIL.equals(credit.getCreditStatus())) {
            // 循环额度 | 调额
            return VoCreditConvert.INSTANCE.toCreditResultDto(credit);
        }

        // 异步授信申请
        mqService.submitCreditApply(credit.getId());
        return VoCreditConvert.INSTANCE.toCreditResultDto(finCreditService.getCredit(credit.getId()));
    }

    /**
     * 资方授信申请
     *
     * @param credit 授信申请记录
     */
    @Override
    public void bankApply(Credit credit) {
        // 资方授信
        CreditResultVo creditResultVo = bankCreditApply(credit);
        // 同步响应状态
        bankCreditApplyResult(credit, creditResultVo);
    }

    /**
     * 查询授信结果， 消息触发
     */
    @Override
    public void query(CreditQueryVo queryVo) {
        Credit credit = finCreditService.getCredit(queryVo.getCreditId());
        if (credit == null) {
            throw new BizException(BizErrorCode.CREDIT_NOT_FOUND);
        }

        CreditStatus creditStatus = credit.getCreditStatus();
        if (creditStatus.isFinal()) {
            return;
        }
        //查询资方授信结果
        CreditResultVo creditResultVo = bankCreditQuery(credit, queryVo);
        ProcessStatus creditResultStatus = creditResultVo.getStatus();
        if (ProcessStatus.PROCESSING.equals(creditResultStatus)) {
            mqService.submitCreditResultQueryDelay(credit.getId());
            return;
        }
        // 授信结果
        credit.setCreditStatus(creditResultStatus.equals(ProcessStatus.SUCCESS) ? CreditStatus.SUCCESS : CreditStatus.FAIL);
        credit.setCreditNo(StringUtil.isBlank(credit.getCreditNo()) ? creditResultVo.getCreditNo() : credit.getCreditNo());
        credit.setCreditContractNo(creditResultVo.getCreditContractNo());
        credit.setRemark(creditResultVo.getFailMsg());
        // 授信成功金额
        credit.setCreditResultAmt(creditResultVo.getCreditResultAmt());
        if (creditResultStatus.equals(ProcessStatus.SUCCESS)) {
            if (Objects.nonNull(creditResultVo.getPassTime())) {
                credit.setPassTime(creditResultVo.getPassTime());
            }
            if (Objects.nonNull(creditResultVo.getCapExpireTime())) {
                credit.setCapExpireTime(creditResultVo.getCapExpireTime());
            }
        }
        // ext授信可用额度
        if (creditResultVo.getStatus().equals(ProcessStatus.SUCCESS)) {
            getCommonService().initCreditAvailable(credit);
        }
        // 授信结果
        finCreditService.updateCredit(credit);
    }


    @Override
    public CreditResultVo failedNotify(CreditApplyVo<ExtInfoVo> apply) {
        // 授信记录
        boolean validate = failedNotifyBankCreditValidate(apply);
        if (!validate) {
            CreditResultVo resultVo = new CreditResultVo();
            resultVo.setStatus(ProcessStatus.FAIL);
            resultVo.setFailMsg("前筛失败");
            return resultVo;
        }
        FailedCredit failedCredit = commonService.failedNotifyCreditApply(apply);
        // 设置新客或者老客，亲家亿联，亲家临商
        failedCredit.setProductType(getProductType());
        failedCredit = commonService.saveFailedCredit(failedCredit);
        mqService.submitCreditFailedNotifyDelay(failedCredit.getId());

        return VoCreditConvert.INSTANCE.toFailedCreditResultDto(finCreditService.getRiskFailedCredit(failedCredit.getId()));
    }

    @Override
    public void bankFailedNotify(FailedCredit credit) {
        // 资方授信
        CreditResultVo resultVo = bankCreditFailedNotify(credit);
        ProcessStatus status = resultVo.getStatus();

        credit.setCreditStatus(status == ProcessStatus.FAIL ? CreditStatus.FAIL : CreditStatus.SUCCESS);
        credit.setRemark(resultVo.getFailMsg());
        commonService.saveFailedCredit(credit);

    }


    @Override
    public BigDecimal getBankRate() {
        return DEFAULT_BANK_RATE;
    }

    /**
     * 调用资方授信查询
     *
     * @param credit 授信
     */
    protected abstract CreditResultVo bankCreditQuery(Credit credit, CreditQueryVo queryVo);

    /**
     * 调用资方授信
     *
     * @param credit 授信
     */
    protected abstract CreditResultVo bankCreditApply(Credit credit);

    protected CreditResultVo bankCreditFailedNotify(FailedCredit credit) {
        CreditResultVo resultVo = new CreditResultVo();
        resultVo.setStatus(ProcessStatus.SUCCESS);
        return resultVo;
    }

    public CreditResultVo recreditApply(RecreditApplyVo recreditApply) {
        return null;
    }

    /**
     * 授信校验
     *
     * @param applyVo 授信
     * @return 是否有效
     */
    protected abstract Credit bankCreditValidate(CreditApplyVo<ExtInfoVo> applyVo);

    /**
     * 授信时间校验
     *
     * @param startTime 授信开始时间 HHmm
     * @param endTime   授信结束时间 HHmm
     */
    protected void creditTimeCheck(String startTime, String endTime) {
        LocalTime now = LocalTime.now();
        LocalTime start = LocalTime.parse(startTime, FORMATTER_HH_MM);
        LocalTime end = LocalTime.parse(endTime, FORMATTER_HH_MM);
        if (now.isBefore(start) || now.isAfter(end)) {
            throw new BizException(BizErrorCode.CREDIT_TIME_INVALID);
        }
    }

    /**
     * 授信金额校验
     *
     * @param creditAmount 当前授信金额
     * @param minAmount    授信最小金额
     * @param maxAmount    授信最大金额
     */
    protected void creditAmountCheck(BigDecimal creditAmount, BigDecimal minAmount, BigDecimal maxAmount) {
        if (creditAmount.compareTo(minAmount) < 0 || creditAmount.compareTo(maxAmount) > 0) {
            throw new BizException(BizErrorCode.CREDIT_AMOUNT_LIMIT);
        }
    }

    /**
     * 授信年龄校验
     *
     * @param idCard 身份证号码
     * @param minAge 最小年龄
     * @param maxAge 最大年龄
     */
    protected void creditAgeCheck(String idCard, int minAge, int maxAge) {
        String birthDay = idCard.substring(BIRTHDAY_INDEX_START, BIRTHDAY_INDEX_END);
        long age = AgeUtil.calcAge(LocalDate.parse(birthDay, DateUtil.SHORT_FORMATTER));
        if (age < minAge || age > maxAge) {
            throw new BizException(BizErrorCode.CREDIT_AGE_LIMIT);
        }
    }

    /**
     * 风控失败的授信校验
     *
     * @param applyVo 授信
     * @return 是否有效
     */
    protected boolean failedNotifyBankCreditValidate(CreditApplyVo<ExtInfoVo> applyVo) {
        return true;
    }

    protected abstract void bankCreditApplyResult(Credit credit, CreditResultVo resultVo);

    public void creditAgreementRecreate(Credit credit) {
        // empty
    }

    protected abstract String getProductType();

    public void contractDownload(String creditId) {
    }

    public void contractUpload(String creditId) {
    }


    @Override
    public void bankSignQuery(String creditId) {

    }

    public void adjustLimitQuery(AdjustLimitBizDto bizDto) {

    }

    /**
     * 额度查询结果回调
     *
     * @param dto
     */
    @Override
    public void quotaQueryResultCallback(QuotaQueryDto dto, QuotaQueryResultDto resultDto) {

    }

    /**
     * 调额结果回调
     *
     * @param adjustId
     */
    @Override
    public void quotaAdjustResultCallback(String adjustId) {
    }

    @Override
    public void userInfoUpdateApply(Credit credit) {

    }

    @Override
    public void userInfoUpdateQuery(Credit credit) {

    }


    protected FinCreditService getFinCreditService() {
        return finCreditService;
    }

    protected MqService getMqService() {
        return mqService;
    }

    protected CommonService getCommonService() {
        return commonService;
    }

    @Override
    public PreCreditApplyResultVo preApply(PreCreditApplyVo preApply) {
        return null;
    }


}

package com.jinghang.capital.core.banks.hxbk.callback.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.jinghang.capital.core.banks.hxbk.dto.repay.HXBKRepayInfoDTO;
import java.util.List;
import java.util.Objects;

/**
 * HXBK 还款回调请求数据
 * 根据接口文档定义的请求参数结构
 *
 * @Author: lhf
 * @CreateTime: 2025/7/17
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class RepayResultCallbackRequest {

    /**
     * 原还款订单号
     */
    @JsonProperty("order_no")
    private String orderNo;
    /**
     * 业务类型
     */
    @JsonProperty("prod_type")
    private String prodType;
    /**
     * 资产方用户唯一标识
     */
    @JsonProperty("open_id")
    private String openId;
    /**
     * 资产方购物订单号
     */
    @JsonProperty("biz_order_no")
    private String bizOrderNo;
    /**
     * 用信申请订单号
     */
    @JsonProperty("original_order_no")
    private String originalOrderNo;
    /**
     * 还款编号
     */
    @JsonProperty("repay_no")
    private String repayNo;
    /**
     * 借据编码
     */
    @JsonProperty("receipt_no")
    private String receiptNo;
    /**
     * 客户编号
     */
    @JsonProperty("custom_no")
    private String customNo;
    /**
     * 还款类型
     */
    @JsonProperty("repay_type")
    private String repayType;
    /**
     * 还款标志:1 线下还款 2 用户主动还款 3 系统代扣
     */
    @JsonProperty("repay_sign")
    private String repaySign;
    /**
     * 还款日期
     */
    @JsonProperty("repay_date")
    private String repayDate;
    /**
     * 还款账户
     */
    @JsonProperty("repay_account")
    private String repayAccount;
    /**
     * 还款状态:Fail:失败 Success:成功 Process:处理中
     */
    @JsonProperty("repay_status")
    private String repayStatus;
    /**
     * 失败原因
     */
    @JsonProperty("fail_reason")
    private String failReason;
    /**
     * 还款信息列表
     */
    @JsonProperty("repay_infos")
    private List<HXBKRepayInfoDTO> repayInfos;

    public String getRepayType() {
        return repayType;
    }

    public void setRepayType(String repayType) {
        this.repayType = repayType;
    }

    public String getCustomNo() {
        return customNo;
    }

    public void setCustomNo(String customNo) {
        this.customNo = customNo;
    }

    public String getReceiptNo() {
        return receiptNo;
    }

    public void setReceiptNo(String receiptNo) {
        this.receiptNo = receiptNo;
    }

    public String getFailReason() {
        return failReason;
    }

    public void setFailReason(String failReason) {
        this.failReason = failReason;
    }

    public String getRepayNo() {
        return repayNo;
    }

    public void setRepayNo(String repayNo) {
        this.repayNo = repayNo;
    }

    public String getRepayAccount() {
        return repayAccount;
    }

    public void setRepayAccount(String repayAccount) {
        this.repayAccount = repayAccount;
    }

    public String getRepayDate() {
        return repayDate;
    }

    public void setRepayDate(String repayDate) {
        this.repayDate = repayDate;
    }

    public String getRepaySign() {
        return repaySign;
    }

    public void setRepaySign(String repaySign) {
        this.repaySign = repaySign;
    }

    public String getRepayStatus() {
        return repayStatus;
    }

    public void setRepayStatus(String repayStatus) {
        this.repayStatus = repayStatus;
    }

    public List<HXBKRepayInfoDTO> getRepayInfos() {
        return repayInfos;
    }

    public void setRepayInfos(List<HXBKRepayInfoDTO> repayInfos) {
        this.repayInfos = repayInfos;
    }

    /**
     * 成功
     *
     * @return 结果
     */
    public boolean isSuccess() { //成功
        return Objects.equals(repayStatus, "Success");
    }

    /**
     * 失败
     *
     * @return 结果
     */
    public boolean isFail() { //失败
        return Objects.equals(repayStatus, "Fail");
    }

    /**
     * 处理中
     *
     * @return 结果
     */
    public boolean isProcess() { //处理中
        return Objects.equals(repayStatus, "Process");
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getOriginalOrderNo() {
        return originalOrderNo;
    }

    public void setOriginalOrderNo(String originalOrderNo) {
        this.originalOrderNo = originalOrderNo;
    }

    public String getBizOrderNo() {
        return bizOrderNo;
    }

    public void setBizOrderNo(String bizOrderNo) {
        this.bizOrderNo = bizOrderNo;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public String getProdType() {
        return prodType;
    }

    public void setProdType(String prodType) {
        this.prodType = prodType;
    }
}

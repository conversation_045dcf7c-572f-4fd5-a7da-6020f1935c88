package com.jinghang.capital.core.banks.hxbk.callback.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * 湖消回调请求公共参数
 *
 * @Author: Lior
 * @CreateTime: 2025/7/8 19:58
 */
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class HXBKCallbackRequest {

    /**
     * 合作方标识
     * 固定值 dubhe
     */
    private String appid;

    /**
     * 业务接口标识
     * 标识唯一的业务接口
     */
    private String method;

    /**
     * 版本号
     * 固定值 1.0
     */
    private String version;

    /**
     * 时间戳
     * yyyy-MM-dd HH:mm:ss
     */
    private String timestamp;

    /**
     * 签名方式
     * 固定值 SHA256WithRSA
     */
    private String signType;

    /**
     * 数字签名
     * 调用方生成的数字签名字符串
     */
    private String sign;

    /**
     * 加密标识
     * 标识业务报文是否加密（加密 1 不加密 0），默认1
     */
    private String encrypt;

    /**
     * 加密秘钥
     * 对称秘钥密文，encrypt=1时必传
     */
    private String secretKey;

    /**
     * 业务参数
     * 具体业务接口的请求参数
     */
    private String data;

    /**
     * 请求ID
     * 标识一次请求，保证唯一
     */
    private String requestId;

    public String getAppid() {
        return appid;
    }

    public void setAppid(String appid) {
        this.appid = appid;
    }

    public String getMethod() {
        return method;
    }

    public void setMethod(String method) {
        this.method = method;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(String timestamp) {
        this.timestamp = timestamp;
    }

    public String getSignType() {
        return signType;
    }

    public void setSignType(String signType) {
        this.signType = signType;
    }

    public String getSign() {
        return sign;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }

    public String getEncrypt() {
        return encrypt;
    }

    public void setEncrypt(String encrypt) {
        this.encrypt = encrypt;
    }

    public String getSecretKey() {
        return secretKey;
    }

    public void setSecretKey(String secretKey) {
        this.secretKey = secretKey;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }
}

package com.jinghang.capital.core.banks.hxbk.dto.credit;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.jinghang.capital.core.banks.hxbk.dto.HXBKBaseRequest;
import com.jinghang.capital.core.banks.hxbk.enums.HXBKMethod;

/**
 * HXBK天枢系统授信申请查询请求
 *
 * @Author: Lior
 * @CreateTime: 2025/7/9 10:00
 */
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class HXBKCreditQueryRequest extends HXBKBaseRequest {

    @JsonIgnore
    private static final HXBKMethod METHOD = HXBKMethod.CREDIT_STATUS_QUERY;

    /**
     * 订单号
     * 必填
     */
    @JsonProperty("order_no")
    private String orderNo;

    /**
     * 授信申请订单号(资产方)
     * 必填
     */
    @JsonProperty("original_order_no")
    private String originalOrderNo;

    /**
     * 三方客户id
     * 非必填
     */
    @JsonProperty("open_id")
    private String openId;

    @Override
    public HXBKMethod getMethod() {
        return METHOD;
    }

    // Getter and Setter methods
    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getOriginalOrderNo() {
        return originalOrderNo;
    }

    public void setOriginalOrderNo(String originalOrderNo) {
        this.originalOrderNo = originalOrderNo;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }
}

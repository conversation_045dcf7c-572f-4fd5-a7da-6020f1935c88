<template>
  <div class="mobile-profile">
    <!-- 退出登录按钮 -->
    <div class="logout-section">
      <van-button type="danger" size="large" block class="logout-btn" @click="handleLogout">
        退出登录
      </van-button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'MobileProfile',
  methods: {
    handleLogout() {
      this.$dialog.confirm({
        title: '提示',
        message: '确定要退出登录吗？'
      }).then(() => {
        this.$store.dispatch('LogOut').then(() => {
          this.$router.push('/mobile/login')
          this.$toast.success('已退出登录')
        })
      }).catch(() => {
        // 用户取消操作
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.mobile-profile {
  background-color: #f5f5f5;
  min-height: calc(100vh - 110px);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.logout-section {
  width: 100%;
  max-width: 300px;

  .logout-btn {
    width: 100%;
    height: 50px;
    font-size: 16px;
    border-radius: 25px;
  }
}

// Vant 按钮样式覆盖
::v-deep .van-button--danger {
  background: linear-gradient(135deg, #ff6b6b, #ee5a52);
  border: none;
}
</style>

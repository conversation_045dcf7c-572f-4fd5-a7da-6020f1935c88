package com.jinghang.ppd.api;

import com.jinghang.ppd.api.dto.RestResult;
import com.jinghang.ppd.api.dto.order.OrderParamDto;
import com.jinghang.ppd.api.dto.order.OrderQueryRequestDto;
import com.jinghang.ppd.api.dto.order.OrderQueryResponseDto;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

public interface OrderApi {
    @PostMapping({"cancel"})
    RestResult<?> cancel(@RequestBody OrderParamDto var1);

    /**
     * 根据订单ID查询订单详情
     * @param requestDto 查询请求
     * @return 订单详情
     */
    @PostMapping("queryByOrderId")
    RestResult<OrderQueryResponseDto> queryByOrderId(@RequestBody OrderQueryRequestDto requestDto);

}

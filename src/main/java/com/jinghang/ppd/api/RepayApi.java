package com.jinghang.ppd.api;


import com.jinghang.ppd.api.dto.RepayTrailDto;
import com.jinghang.ppd.api.dto.RestResult;
import com.jinghang.ppd.api.dto.TrailResultDto;
import com.jinghang.ppd.api.dto.repay.*;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 还款api
 */
public interface RepayApi {

    @PostMapping("trial")
    RestResult<TrailResultDto> trial(@RequestBody RepayTrailDto trailDto);

    /**
     * 减免申请
     * @param reduceApplyDto
     * @return
     */
    @PostMapping("reduceApply")
    RestResult<Void> reduceApply(@RequestBody ReduceApplyDto reduceApplyDto);

    /**
     * 还款销账
     * @param reduceApplyDto
     * @return
     */
    @PostMapping("repayApply")
    RestResult<Void> repayApply(@RequestBody RepayApplyDto reduceApplyDto);

    /**
     * 线下还款
     * @param settleRepayApplyDto
     * @return
     */
    @PostMapping("settleRepayApply")
    RestResult<Void> settleRepayApply(@RequestBody SettleRepayApplyDto settleRepayApplyDto);

    /**
     * 结清试算
     * @param settleTrialDto 结清试算请求
     * @return 试算结果，结构与普通试算一致
     */
    @PostMapping("settleTrial")
    RestResult<TrailResultDto> settleTrial(@RequestBody SettleTrialDto settleTrialDto);
}

package com.jinghang.ppd.api.dto.repay;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

public class RepayPlanQueryResponseDTO {
    private String orderId;
    private String userName;
    private String certNo;
    private List<RepayPlanDto> repayPlans;

    public RepayPlanQueryResponseDTO() {
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getUserName() {
        return this.userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getCertNo() {
        return this.certNo;
    }

    public void setCertNo(String certNo) {
        this.certNo = certNo;
    }

    public List<RepayPlanDto> getRepayPlans() {
        return this.repayPlans;
    }

    public void setRepayPlans(List<RepayPlanDto> repayPlans) {
        this.repayPlans = repayPlans;
    }

    public static class RepayPlanDto {

        private String userId;
        private String loanId;
        private Integer period;
        private LocalDate planRepayDate;
        private BigDecimal principalAmt;
        private BigDecimal interestAmt;
        private BigDecimal guaranteeAmt;
        private BigDecimal penaltyAmt;
        private BigDecimal amount;
        private BigDecimal consultFee;
        /**
         * NORMAL,
         * REPAID
         */
        private String custRepayState;

        private LocalDateTime actRepayTime;
        private BigDecimal actPrincipalAmt;

        private BigDecimal actInterestAmt;

        private BigDecimal actGuaranteeAmt;

        private BigDecimal actPenaltyAmt;

        private BigDecimal actCapitalPenaltyAmt;

        private BigDecimal actBreachAmt;

        private BigDecimal actAmount;

        private BigDecimal actConsultFee;


        public RepayPlanDto() {
        }

        public String getUserId() {
            return userId;
        }

        public void setUserId(String userId) {
            this.userId = userId;
        }

        public String getLoanId() {
            return loanId;
        }

        public void setLoanId(String loanId) {
            this.loanId = loanId;
        }

        public Integer getPeriod() {
            return period;
        }

        public void setPeriod(Integer period) {
            this.period = period;
        }

        public LocalDate getPlanRepayDate() {
            return planRepayDate;
        }

        public void setPlanRepayDate(LocalDate planRepayDate) {
            this.planRepayDate = planRepayDate;
        }

        public BigDecimal getPrincipalAmt() {
            return principalAmt;
        }

        public void setPrincipalAmt(BigDecimal principalAmt) {
            this.principalAmt = principalAmt;
        }

        public BigDecimal getInterestAmt() {
            return interestAmt;
        }

        public void setInterestAmt(BigDecimal interestAmt) {
            this.interestAmt = interestAmt;
        }

        public BigDecimal getGuaranteeAmt() {
            return guaranteeAmt;
        }

        public void setGuaranteeAmt(BigDecimal guaranteeAmt) {
            this.guaranteeAmt = guaranteeAmt;
        }

        public BigDecimal getPenaltyAmt() {
            return penaltyAmt;
        }

        public void setPenaltyAmt(BigDecimal penaltyAmt) {
            this.penaltyAmt = penaltyAmt;
        }

        public BigDecimal getAmount() {
            return amount;
        }

        public void setAmount(BigDecimal amount) {
            this.amount = amount;
        }

        public BigDecimal getConsultFee() {
            return consultFee;
        }

        public void setConsultFee(BigDecimal consultFee) {
            this.consultFee = consultFee;
        }

        public String getCustRepayState() {
            return custRepayState;
        }

        public void setCustRepayState(String custRepayState) {
            this.custRepayState = custRepayState;
        }

        public LocalDateTime getActRepayTime() {
            return actRepayTime;
        }

        public void setActRepayTime(LocalDateTime actRepayTime) {
            this.actRepayTime = actRepayTime;
        }

        public BigDecimal getActPrincipalAmt() {
            return actPrincipalAmt;
        }

        public void setActPrincipalAmt(BigDecimal actPrincipalAmt) {
            this.actPrincipalAmt = actPrincipalAmt;
        }

        public BigDecimal getActInterestAmt() {
            return actInterestAmt;
        }

        public void setActInterestAmt(BigDecimal actInterestAmt) {
            this.actInterestAmt = actInterestAmt;
        }

        public BigDecimal getActGuaranteeAmt() {
            return actGuaranteeAmt;
        }

        public void setActGuaranteeAmt(BigDecimal actGuaranteeAmt) {
            this.actGuaranteeAmt = actGuaranteeAmt;
        }

        public BigDecimal getActPenaltyAmt() {
            return actPenaltyAmt;
        }

        public void setActPenaltyAmt(BigDecimal actPenaltyAmt) {
            this.actPenaltyAmt = actPenaltyAmt;
        }

        public BigDecimal getActCapitalPenaltyAmt() {
            return actCapitalPenaltyAmt;
        }

        public void setActCapitalPenaltyAmt(BigDecimal actCapitalPenaltyAmt) {
            this.actCapitalPenaltyAmt = actCapitalPenaltyAmt;
        }

        public BigDecimal getActBreachAmt() {
            return actBreachAmt;
        }

        public void setActBreachAmt(BigDecimal actBreachAmt) {
            this.actBreachAmt = actBreachAmt;
        }

        public BigDecimal getActAmount() {
            return actAmount;
        }

        public void setActAmount(BigDecimal actAmount) {
            this.actAmount = actAmount;
        }

        public BigDecimal getActConsultFee() {
            return actConsultFee;
        }

        public void setActConsultFee(BigDecimal actConsultFee) {
            this.actConsultFee = actConsultFee;
        }
    }
}

package com.jinghang.ppd.api.dto.repay;

import com.jinghang.ppd.api.enums.RepayPurpose;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @title:
 * @desc:
 * @date 2025/9/18 09:14
 */
public class SettleRepayApplyDto {

    /**
     * 还款方式
     */
    private RepayPurpose repayPurpose;

    /**
     * 期数
     */
    private Integer period;

    /**
     * 订单id
     */
    private String orderId;

    /**
     * 实还金额
     */
    private BigDecimal actAmount;

    /**
     * 溢出金额
     */
    private BigDecimal overflowAmount;

    private String outerRepayNo;

    private String repayDate;

    public BigDecimal getOverflowAmount() {
        return overflowAmount;
    }

    public void setOverflowAmount(BigDecimal overflowAmount) {
        this.overflowAmount = overflowAmount;
    }

    public RepayPurpose getRepayPurpose() {
        return repayPurpose;
    }

    public void setRepayPurpose(RepayPurpose repayPurpose) {
        this.repayPurpose = repayPurpose;
    }

    public Integer getPeriod() {
        return period;
    }

    public void setPeriod(Integer period) {
        this.period = period;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public BigDecimal getActAmount() {
        return actAmount;
    }

    public void setActAmount(BigDecimal actAmount) {
        this.actAmount = actAmount;
    }

    public String getOuterRepayNo() {
        return outerRepayNo;
    }

    public void setOuterRepayNo(String outerRepayNo) {
        this.outerRepayNo = outerRepayNo;
    }

    public String getRepayDate() {
        return repayDate;
    }

    public void setRepayDate(String repayDate) {
        this.repayDate = repayDate;
    }
}

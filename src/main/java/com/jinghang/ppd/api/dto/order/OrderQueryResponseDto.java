package com.jinghang.ppd.api.dto.order;


import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

public class OrderQueryResponseDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /*** 订单ID (主键)
     */
    private String orderId;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 流量渠道
     */
    private String flowChannel;

    /**
     * 额度类型
     */
    private String amountType;

    /**
     * 应用来源
     */
    private String applicationSource;


    /**
     * 是否为续借
     */
    private String renewedFlag;


    /**
     * 渠道标签
     */
    private String applyChannel;


    /**
     * 外部用户id
     */
    private String openId;


    /**
     * 外部订单号
     */
    private String outerOrderId;


    /**
     * 风控ID
     */
    private String riskId;


    /**
     * 申请时间
     */
    private LocalDateTime applyTime;

    /**
     * 申请金额
     */
    private BigDecimal applyAmount;

    /**
     * 申请期数
     */
    private Integer applyPeriods;


    /**
     * 借款用途
     */
    private String loanPurpose;

    /**
     * 订单状态
     */
    private String orderState;


    /**
     * 放款成功时间
     */
    private LocalDateTime loanTime;


    /**
     * 放款卡id
     */
    private String loanCardId;

    /**
     * 审批金额
     */
    private BigDecimal approveAmount;

    /**
     * 资方渠道
     */
    private String bankChannel;


    /**
     * 月供
     */
    private BigDecimal monthPay;


    /**
     * 对客利率
     */
    private BigDecimal irrRate;

    /**
     * '对资利率'
     */
    private BigDecimal bankRate;

    /**
     * 风控审批利率
     */
    private BigDecimal approveRate;


    /**
     * 是否强制购买权益
     */
    private BigDecimal approveRightsForce;


    /**
     * 审批权益等级
     */
    private BigDecimal approveRights;


    /**
     * 权益打标记号
     */
    private BigDecimal rightsMarking;

    /**
     * '权益包id'
     */
    private String rightsPackageId;

    /**
     * '是否已绑资方卡'
     */
    private String bindCapitalCardState;

    /**
     * 'h5订单提交放款'
     */
    private String orderSubmitState;


    /**
     * 姓名
     */
    private String name;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 身份证号
     */
    private String certNo;


    public OrderQueryResponseDto() {
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getFlowChannel() {
        return flowChannel;
    }

    public void setFlowChannel(String flowChannel) {
        this.flowChannel = flowChannel;
    }

    public String getAmountType() {
        return amountType;
    }

    public void setAmountType(String amountType) {
        this.amountType = amountType;
    }

    public String getApplicationSource() {
        return applicationSource;
    }

    public void setApplicationSource(String applicationSource) {
        this.applicationSource = applicationSource;
    }

    public String getRenewedFlag() {
        return renewedFlag;
    }

    public void setRenewedFlag(String renewedFlag) {
        this.renewedFlag = renewedFlag;
    }

    public String getApplyChannel() {
        return applyChannel;
    }

    public void setApplyChannel(String applyChannel) {
        this.applyChannel = applyChannel;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public String getOuterOrderId() {
        return outerOrderId;
    }

    public void setOuterOrderId(String outerOrderId) {
        this.outerOrderId = outerOrderId;
    }

    public String getRiskId() {
        return riskId;
    }

    public void setRiskId(String riskId) {
        this.riskId = riskId;
    }

    public LocalDateTime getApplyTime() {
        return applyTime;
    }

    public void setApplyTime(LocalDateTime applyTime) {
        this.applyTime = applyTime;
    }

    public BigDecimal getApplyAmount() {
        return applyAmount;
    }

    public void setApplyAmount(BigDecimal applyAmount) {
        this.applyAmount = applyAmount;
    }

    public Integer getApplyPeriods() {
        return applyPeriods;
    }

    public void setApplyPeriods(Integer applyPeriods) {
        this.applyPeriods = applyPeriods;
    }

    public String getLoanPurpose() {
        return loanPurpose;
    }

    public void setLoanPurpose(String loanPurpose) {
        this.loanPurpose = loanPurpose;
    }

    public String getOrderState() {
        return orderState;
    }

    public void setOrderState(String orderState) {
        this.orderState = orderState;
    }

    public LocalDateTime getLoanTime() {
        return loanTime;
    }

    public void setLoanTime(LocalDateTime loanTime) {
        this.loanTime = loanTime;
    }

    public String getLoanCardId() {
        return loanCardId;
    }

    public void setLoanCardId(String loanCardId) {
        this.loanCardId = loanCardId;
    }

    public BigDecimal getApproveAmount() {
        return approveAmount;
    }

    public void setApproveAmount(BigDecimal approveAmount) {
        this.approveAmount = approveAmount;
    }

    public String getBankChannel() {
        return bankChannel;
    }

    public void setBankChannel(String bankChannel) {
        this.bankChannel = bankChannel;
    }

    public BigDecimal getMonthPay() {
        return monthPay;
    }

    public void setMonthPay(BigDecimal monthPay) {
        this.monthPay = monthPay;
    }

    public BigDecimal getIrrRate() {
        return irrRate;
    }

    public void setIrrRate(BigDecimal irrRate) {
        this.irrRate = irrRate;
    }

    public BigDecimal getBankRate() {
        return bankRate;
    }

    public void setBankRate(BigDecimal bankRate) {
        this.bankRate = bankRate;
    }

    public BigDecimal getApproveRate() {
        return approveRate;
    }

    public void setApproveRate(BigDecimal approveRate) {
        this.approveRate = approveRate;
    }

    public BigDecimal getApproveRightsForce() {
        return approveRightsForce;
    }

    public void setApproveRightsForce(BigDecimal approveRightsForce) {
        this.approveRightsForce = approveRightsForce;
    }

    public BigDecimal getApproveRights() {
        return approveRights;
    }

    public void setApproveRights(BigDecimal approveRights) {
        this.approveRights = approveRights;
    }

    public BigDecimal getRightsMarking() {
        return rightsMarking;
    }

    public void setRightsMarking(BigDecimal rightsMarking) {
        this.rightsMarking = rightsMarking;
    }

    public String getRightsPackageId() {
        return rightsPackageId;
    }

    public void setRightsPackageId(String rightsPackageId) {
        this.rightsPackageId = rightsPackageId;
    }

    public String getBindCapitalCardState() {
        return bindCapitalCardState;
    }

    public void setBindCapitalCardState(String bindCapitalCardState) {
        this.bindCapitalCardState = bindCapitalCardState;
    }

    public String getOrderSubmitState() {
        return orderSubmitState;
    }

    public void setOrderSubmitState(String orderSubmitState) {
        this.orderSubmitState = orderSubmitState;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getCertNo() {
        return certNo;
    }

    public void setCertNo(String certNo) {
        this.certNo = certNo;
    }
}
package com.jinghang.ppd.api.dto.repay;



import com.jinghang.ppd.api.enums.RepayPurpose;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
public class RepayApplyDto {

    /**
     * 还款方式
     */
    private RepayPurpose repayPurpose;

    /**
     * 期数
     */
    private Integer period;

    /**
     * 订单id
     */
    private String orderId;

    /**
     * 实还金额
     */
    private BigDecimal amount;


    public RepayPurpose getRepayPurpose() {
        return repayPurpose;
    }

    public void setRepayPurpose(RepayPurpose repayPurpose) {
        this.repayPurpose = repayPurpose;
    }

    public Integer getPeriod() {
        return period;
    }

    public void setPeriod(Integer period) {
        this.period = period;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

}

package com.jinghang.ppd.api.dto.collection;



import java.math.BigDecimal;

public class PpdReduceApplyRequest   {

    /**
     * 放款请求流水号
     * 唯一放款请求流水号
     */
    private String loanReqNo;

    /**
     * 申请单号
     * 申请单号(唯一)
     */
    private String applyNo;

    /**
     * 请求方代码
     * 示例值: channelB
     */
    private String sourceCode;

    /**
     * 申请期次
     * 期次数值
     */
    private Integer applyTerm;

    /**
     * 申请金额
     * 申请减免金额 单位元
     */
    private BigDecimal applyReductAmount;







    public String getLoanReqNo() {
        return loanReqNo;
    }

    public void setLoanReqNo(String loanReqNo) {
        this.loanReqNo = loanReqNo;
    }

    public String getApplyNo() {
        return applyNo;
    }

    public void setApplyNo(String applyNo) {
        this.applyNo = applyNo;
    }

    public String getSourceCode() {
        return sourceCode;
    }

    public void setSourceCode(String sourceCode) {
        this.sourceCode = sourceCode;
    }


    public Integer getApplyTerm() {
        return applyTerm;
    }

    public void setApplyTerm(Integer applyTerm) {
        this.applyTerm = applyTerm;
    }

    public BigDecimal getApplyReductAmount() {
        return applyReductAmount;
    }

    public void setApplyReductAmount(BigDecimal applyReductAmount) {
        this.applyReductAmount = applyReductAmount;
    }

//    @Override
//    public PpdCallBackApi getApiType() {
//        return PpdCallBackApi.REDUCE_APPLY;
//    }
}
